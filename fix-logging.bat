@echo off
echo Removing SLF4J dependencies and replacing with simple console logging...

REM Create a simple Java class to do the replacements
echo Creating LoggingFixer.java...

echo import java.io.*; > LoggingFixer.java
echo import java.nio.file.*; >> LoggingFixer.java
echo import java.util.regex.*; >> LoggingFixer.java
echo. >> LoggingFixer.java
echo public class LoggingFixer { >> LoggingFixer.java
echo     public static void main(String[] args) throws IOException { >> LoggingFixer.java
echo         String[] files = { >> LoggingFixer.java
echo             "src/main/java/com/airsnap/integration/impl/AirSnapCoreServiceImpl.java", >> LoggingFixer.java
echo             "src/main/java/com/airsnap/integration/impl/CryptographyServiceImpl.java", >> LoggingFixer.java
echo             "src/main/java/com/airsnap/integration/impl/FaceRecognitionServiceImpl.java", >> LoggingFixer.java
echo             "src/main/java/com/airsnap/integration/impl/FingerprintServiceImpl.java", >> LoggingFixer.java
echo             "src/main/java/com/airsnap/integration/impl/UtilityServiceImpl.java" >> LoggingFixer.java
echo         }; >> LoggingFixer.java
echo. >> LoggingFixer.java
echo         for (String file : files) { >> LoggingFixer.java
echo             System.out.println("Processing: " + file); >> LoggingFixer.java
echo             String content = Files.readString(Paths.get(file)); >> LoggingFixer.java
echo. >> LoggingFixer.java
echo             // Remove SLF4J imports >> LoggingFixer.java
echo             content = content.replaceAll("import org\\.slf4j\\.Logger;\\s*", ""); >> LoggingFixer.java
echo             content = content.replaceAll("import org\\.slf4j\\.LoggerFactory;\\s*", ""); >> LoggingFixer.java
echo. >> LoggingFixer.java
echo             // Replace logger declaration >> LoggingFixer.java
echo             content = content.replaceAll( >> LoggingFixer.java
echo                 "private static final Logger logger = LoggerFactory\\.getLogger\\([^)]+\\);", >> LoggingFixer.java
echo                 "private void log(String message) { System.out.println(\"[\" + getClass().getSimpleName() + \"] \" + message); }" >> LoggingFixer.java
echo             ); >> LoggingFixer.java
echo. >> LoggingFixer.java
echo             // Replace logger calls >> LoggingFixer.java
echo             content = content.replaceAll("logger\\.info\\(\"([^\"]+)\"\\)", "log(\"$1\")"); >> LoggingFixer.java
echo             content = content.replaceAll("logger\\.debug\\(\"([^\"]+)\"\\)", "log(\"$1\")"); >> LoggingFixer.java
echo             content = content.replaceAll("logger\\.error\\(\"([^\"]+)\"[^)]*\\)", "log(\"$1\")"); >> LoggingFixer.java
echo. >> LoggingFixer.java
echo             Files.writeString(Paths.get(file), content); >> LoggingFixer.java
echo             System.out.println("Fixed: " + file); >> LoggingFixer.java
echo         } >> LoggingFixer.java
echo     } >> LoggingFixer.java
echo } >> LoggingFixer.java

echo Compiling and running LoggingFixer...
javac LoggingFixer.java
java LoggingFixer

echo Cleaning up...
del LoggingFixer.java LoggingFixer.class

echo Done! SLF4J dependencies removed from all implementation files.
