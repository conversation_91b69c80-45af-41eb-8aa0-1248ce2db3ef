import 'dart:io';
import 'package:flutter/material.dart';
import 'camera_capture_widget.dart';

class FaceCaptureWidget extends StatelessWidget {
  final Function(File) onFaceCaptured;
  final String? instructions;

  const FaceCaptureWidget({
    super.key,
    required this.onFaceCaptured,
    this.instructions,
  });

  @override
  Widget build(BuildContext context) {
    return CameraCaptureWidget(
      title: 'Capture Face Photo',
      subtitle: instructions ?? 
          'Position your face in the oval frame\nEnsure good lighting and look directly at the camera',
      onImageCaptured: onFaceCaptured,
      preferredLens: CameraLensDirection.front,
      showGalleryOption: true,
      overlayWidget: const FaceOverlayWidget(),
    );
  }
}

class FaceOverlayWidget extends StatelessWidget {
  const FaceOverlayWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Dark overlay with oval cutout
        Container(
          color: Colors.black.withOpacity(0.5),
          child: Center(
            child: Container(
              width: 280,
              height: 350,
              decoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(140),
                border: Border.all(
                  color: Colors.white,
                  width: 3,
                ),
              ),
            ),
          ),
        ),

        // Face guide overlay
        Center(
          child: CustomPaint(
            size: const Size(280, 350),
            painter: FaceGuidePainter(),
          ),
        ),

        // Instructions overlay
        Positioned(
          bottom: 150,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.face,
                  color: Colors.white,
                  size: 32,
                ),
                SizedBox(height: 8),
                Text(
                  'Face Capture Guidelines:',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  '• Keep your face centered in the oval\n'
                  '• Look directly at the camera\n'
                  '• Ensure good lighting\n'
                  '• Remove glasses if possible\n'
                  '• Keep a neutral expression',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class FaceGuidePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double centerX = size.width / 2;
    final double centerY = size.height / 2;

    // Draw face outline guide
    final Path facePath = Path();
    facePath.addOval(Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: size.width * 0.8,
      height: size.height * 0.85,
    ));

    canvas.drawPath(facePath, paint);

    // Draw eye guides
    final double eyeY = centerY - 30;
    final double eyeDistance = 40;
    
    // Left eye
    canvas.drawCircle(
      Offset(centerX - eyeDistance, eyeY),
      8,
      paint,
    );
    
    // Right eye
    canvas.drawCircle(
      Offset(centerX + eyeDistance, eyeY),
      8,
      paint,
    );

    // Draw nose guide
    canvas.drawLine(
      Offset(centerX, centerY - 10),
      Offset(centerX, centerY + 10),
      paint,
    );

    // Draw mouth guide
    final Path mouthPath = Path();
    mouthPath.moveTo(centerX - 20, centerY + 40);
    mouthPath.quadraticBezierTo(
      centerX, centerY + 50,
      centerX + 20, centerY + 40,
    );
    canvas.drawPath(mouthPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class FaceCaptureDialog extends StatefulWidget {
  final Function(File) onFaceCaptured;

  const FaceCaptureDialog({
    super.key,
    required this.onFaceCaptured,
  });

  @override
  State<FaceCaptureDialog> createState() => _FaceCaptureDialogState();
}

class _FaceCaptureDialogState extends State<FaceCaptureDialog> {
  File? _capturedFace;

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: FaceCaptureWidget(
        onFaceCaptured: (File faceFile) {
          setState(() {
            _capturedFace = faceFile;
          });
          widget.onFaceCaptured(faceFile);
          Navigator.of(context).pop(faceFile);
        },
        instructions: 'Position your face in the guide and capture',
      ),
    );
  }
}

// Face capture result widget
class FaceCaptureResult extends StatelessWidget {
  final File? faceImage;
  final VoidCallback? onRetake;
  final VoidCallback? onConfirm;

  const FaceCaptureResult({
    super.key,
    this.faceImage,
    this.onRetake,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.face,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                const Text(
                  'Face Photo',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (faceImage != null) ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    faceImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onRetake,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retake'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onConfirm,
                      icon: const Icon(Icons.check),
                      label: const Text('Confirm'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.face,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'No face photo captured',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onRetake,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Capture Face Photo'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Face quality validator
class FaceQualityValidator {
  static Future<FaceQualityResult> validateFace(File faceImage) async {
    try {
      // In a real implementation, this would use ML Kit or similar
      // to validate face quality, lighting, pose, etc.
      
      final bool exists = await faceImage.exists();
      if (!exists) {
        return FaceQualityResult(
          isValid: false,
          score: 0.0,
          issues: ['File does not exist'],
        );
      }

      final int fileSize = await faceImage.length();
      if (fileSize < 1000) {
        return FaceQualityResult(
          isValid: false,
          score: 0.2,
          issues: ['Image file too small'],
        );
      }

      // Mock validation - in real implementation would analyze:
      // - Face detection
      // - Lighting quality
      // - Face pose
      // - Image sharpness
      // - Eye visibility
      
      return FaceQualityResult(
        isValid: true,
        score: 0.85,
        issues: [],
        recommendations: [
          'Good face capture quality',
          'Suitable for biometric processing',
        ],
      );
    } catch (e) {
      return FaceQualityResult(
        isValid: false,
        score: 0.0,
        issues: ['Validation error: $e'],
      );
    }
  }
}

class FaceQualityResult {
  final bool isValid;
  final double score;
  final List<String> issues;
  final List<String> recommendations;

  FaceQualityResult({
    required this.isValid,
    required this.score,
    this.issues = const [],
    this.recommendations = const [],
  });

  String get qualityDescription {
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.6) return 'Good';
    if (score >= 0.4) return 'Fair';
    if (score >= 0.2) return 'Poor';
    return 'Very Poor';
  }
}
