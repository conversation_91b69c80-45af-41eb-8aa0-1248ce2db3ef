package com.airsnap.cryptograph.model;

import java.time.LocalDateTime;

/**
 * Model class representing biometric data (face and fingerprint) for cryptograph generation
 */
public class BiometricData {
    private Long id;
    private Long demographicId;
    private byte[] faceTemplate;
    private byte[] faceImage;
    private byte[] fingerprintTemplate;
    private byte[] fingerprintImage;
    private byte[] fingerprintMinutiae;
    private LocalDateTime createdAt;
    
    // Constructors
    public BiometricData() {}
    
    public BiometricData(Long demographicId) {
        this.demographicId = demographicId;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getDemographicId() { return demographicId; }
    public void setDemographicId(Long demographicId) { this.demographicId = demographicId; }
    
    public byte[] getFaceTemplate() { return faceTemplate; }
    public void setFaceTemplate(byte[] faceTemplate) { this.faceTemplate = faceTemplate; }
    
    public byte[] getFaceImage() { return faceImage; }
    public void setFaceImage(byte[] faceImage) { this.faceImage = faceImage; }
    
    public byte[] getFingerprintTemplate() { return fingerprintTemplate; }
    public void setFingerprintTemplate(byte[] fingerprintTemplate) { this.fingerprintTemplate = fingerprintTemplate; }
    
    public byte[] getFingerprintImage() { return fingerprintImage; }
    public void setFingerprintImage(byte[] fingerprintImage) { this.fingerprintImage = fingerprintImage; }
    
    public byte[] getFingerprintMinutiae() { return fingerprintMinutiae; }
    public void setFingerprintMinutiae(byte[] fingerprintMinutiae) { this.fingerprintMinutiae = fingerprintMinutiae; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public boolean hasFaceData() {
        return faceTemplate != null && faceTemplate.length > 0;
    }
    
    public boolean hasFingerprintData() {
        return fingerprintTemplate != null && fingerprintTemplate.length > 0;
    }
    
    @Override
    public String toString() {
        return "BiometricData{" +
                "id=" + id +
                ", demographicId=" + demographicId +
                ", hasFaceData=" + hasFaceData() +
                ", hasFingerprintData=" + hasFingerprintData() +
                ", createdAt=" + createdAt +
                '}';
    }
}
