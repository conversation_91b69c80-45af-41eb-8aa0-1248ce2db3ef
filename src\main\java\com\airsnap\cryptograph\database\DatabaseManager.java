package com.airsnap.cryptograph.database;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Database manager for the AirSnap Cryptograph System
 * Handles SQLite database operations for storing cryptographs, demographic data, and audit history
 */
public class DatabaseManager {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    private static final String DB_NAME = "cryptograph_system.db";
    private static final String DB_URL = "jdbc:sqlite:" + DB_NAME;
    
    private Connection connection;
    
    public DatabaseManager() {
        initializeDatabase();
    }
    
    /**
     * Initialize the database and create tables if they don't exist
     */
    private void initializeDatabase() {
        try {
            connection = DriverManager.getConnection(DB_URL);
            createTables();
            logger.info("Database initialized successfully");
        } catch (SQLException e) {
            logger.error("Failed to initialize database", e);
            throw new RuntimeException("Database initialization failed", e);
        }
    }
    
    /**
     * Create all necessary tables
     */
    private void createTables() throws SQLException {
        // Demographic information table
        String createDemographicTable = """
            CREATE TABLE IF NOT EXISTS demographic_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                date_of_birth DATE,
                gender TEXT,
                nationality TEXT,
                document_number TEXT,
                document_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        // Biometric data table
        String createBiometricTable = """
            CREATE TABLE IF NOT EXISTS biometric_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                demographic_id INTEGER NOT NULL,
                face_template BLOB,
                face_image BLOB,
                fingerprint_template BLOB,
                fingerprint_image BLOB,
                fingerprint_minutiae BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (demographic_id) REFERENCES demographic_info(id)
            )
        """;
        
        // Cryptographs table
        String createCryptographTable = """
            CREATE TABLE IF NOT EXISTS cryptographs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cryptograph_id TEXT UNIQUE NOT NULL,
                demographic_id INTEGER NOT NULL,
                biometric_id INTEGER NOT NULL,
                cryptograph_data BLOB NOT NULL,
                cryptograph_hash TEXT NOT NULL,
                generation_method TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (demographic_id) REFERENCES demographic_info(id),
                FOREIGN KEY (biometric_id) REFERENCES biometric_data(id)
            )
        """;
        
        // Scan history table
        String createScanHistoryTable = """
            CREATE TABLE IF NOT EXISTS scan_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cryptograph_id TEXT NOT NULL,
                scan_result TEXT NOT NULL,
                extracted_data TEXT,
                scan_confidence REAL,
                scan_method TEXT,
                scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        // Comparison history table
        String createComparisonHistoryTable = """
            CREATE TABLE IF NOT EXISTS comparison_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comparison_type TEXT NOT NULL,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                comparison_score REAL NOT NULL,
                match_result BOOLEAN NOT NULL,
                comparison_method TEXT,
                comparison_details TEXT,
                compared_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        // Audit log table
        String createAuditLogTable = """
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operation_type TEXT NOT NULL,
                entity_type TEXT NOT NULL,
                entity_id TEXT,
                user_info TEXT,
                operation_details TEXT,
                success BOOLEAN NOT NULL,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        // Execute table creation
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createDemographicTable);
            stmt.execute(createBiometricTable);
            stmt.execute(createCryptographTable);
            stmt.execute(createScanHistoryTable);
            stmt.execute(createComparisonHistoryTable);
            stmt.execute(createAuditLogTable);
            logger.info("All database tables created successfully");
        }
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * Close database connection
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                logger.info("Database connection closed");
            }
        } catch (SQLException e) {
            logger.error("Error closing database connection", e);
        }
    }
    
    /**
     * Execute a query and return results
     */
    public ResultSet executeQuery(String sql, Object... params) throws SQLException {
        PreparedStatement stmt = connection.prepareStatement(sql);
        for (int i = 0; i < params.length; i++) {
            stmt.setObject(i + 1, params[i]);
        }
        return stmt.executeQuery();
    }
    
    /**
     * Execute an update/insert/delete statement
     */
    public int executeUpdate(String sql, Object... params) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            return stmt.executeUpdate();
        }
    }
    
    /**
     * Log audit information
     */
    public void logAudit(String operationType, String entityType, String entityId, 
                        String userInfo, String details, boolean success, String errorMessage) {
        String sql = """
            INSERT INTO audit_log (operation_type, entity_type, entity_id, user_info, 
                                 operation_details, success, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try {
            executeUpdate(sql, operationType, entityType, entityId, userInfo, details, success, errorMessage);
        } catch (SQLException e) {
            logger.error("Failed to log audit information", e);
        }
    }
}
