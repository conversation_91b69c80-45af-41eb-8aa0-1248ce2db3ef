package com.airsnap.cryptograph;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.service.*;
import com.airsnap.cryptograph.ui.MainController;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Main JavaFX Application for AirSnap Cryptograph System
 */
public class CryptographApplication extends Application {
    private static final Logger logger = LoggerFactory.getLogger(CryptographApplication.class);
    
    private DatabaseManager databaseManager;
    private CryptographGenerationService generationService;
    private CryptographScanningService scanningService;
    private BiometricComparisonService comparisonService;
    private HistoryAndAuditService historyService;
    
    @Override
    public void start(Stage primaryStage) {
        try {
            logger.info("Starting AirSnap Cryptograph System...");
            
            // Initialize services
            initializeServices();
            
            // Load FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/main.fxml"));
            Scene scene = new Scene(loader.load(), 1200, 800);
            
            // Get controller and inject services
            MainController controller = loader.getController();
            controller.initializeServices(generationService, scanningService, comparisonService, historyService);
            
            // Configure stage
            primaryStage.setTitle("AirSnap Cryptograph System");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(1000);
            primaryStage.setMinHeight(700);
            
            // Handle application close
            primaryStage.setOnCloseRequest(event -> {
                logger.info("Application closing...");
                cleanup();
            });
            
            primaryStage.show();
            logger.info("AirSnap Cryptograph System started successfully");
            
        } catch (Exception e) {
            logger.error("Failed to start application", e);
            showErrorAlert("Application Startup Error", 
                          "Failed to start the AirSnap Cryptograph System", 
                          e.getMessage());
        }
    }
    
    /**
     * Initialize all services
     */
    private void initializeServices() {
        try {
            // Initialize database
            databaseManager = new DatabaseManager();
            logger.info("Database initialized");
            
            // Initialize services
            generationService = new CryptographGenerationService(databaseManager);
            scanningService = new CryptographScanningService(databaseManager);
            comparisonService = new BiometricComparisonService(databaseManager);
            historyService = new HistoryAndAuditService(databaseManager);
            
            logger.info("All services initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize services", e);
            throw new RuntimeException("Service initialization failed", e);
        }
    }
    
    /**
     * Cleanup resources on application exit
     */
    private void cleanup() {
        try {
            if (generationService != null) {
                generationService.cleanup();
            }
            
            if (databaseManager != null) {
                databaseManager.close();
            }
            
            logger.info("Application cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error during cleanup", e);
        }
    }
    
    /**
     * Show error alert dialog
     */
    private void showErrorAlert(String title, String header, String content) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(header);
        alert.setContentText(content);
        alert.showAndWait();
    }
    
    /**
     * Main method
     */
    public static void main(String[] args) {
        // Set system properties for JavaFX
        System.setProperty("javafx.preloader", "com.sun.javafx.application.LauncherImpl");
        
        // Log startup
        Logger startupLogger = LoggerFactory.getLogger(CryptographApplication.class);
        startupLogger.info("=== AirSnap Cryptograph System Starting ===");
        startupLogger.info("Java Version: {}", System.getProperty("java.version"));
        startupLogger.info("JavaFX Version: {}", System.getProperty("javafx.version"));
        startupLogger.info("Operating System: {} {}", 
                          System.getProperty("os.name"), 
                          System.getProperty("os.version"));
        
        try {
            launch(args);
        } catch (Exception e) {
            startupLogger.error("Failed to launch application", e);
            System.exit(1);
        }
    }
}
