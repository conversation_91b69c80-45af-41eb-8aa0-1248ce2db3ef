package com.airsnap.cryptograph;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.DemographicInfo;
import com.airsnap.cryptograph.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for the AirSnap Cryptograph System
 */
public class CryptographSystemTest {
    private static final Logger logger = LoggerFactory.getLogger(CryptographSystemTest.class);
    
    private DatabaseManager databaseManager;
    private CryptographGenerationService generationService;
    private CryptographScanningService scanningService;
    private BiometricComparisonService comparisonService;
    private HistoryAndAuditService historyService;
    
    @BeforeEach
    void setUp() {
        try {
            logger.info("Setting up test environment...");
            
            // Initialize database with test configuration
            databaseManager = new DatabaseManager();
            
            // Initialize services
            generationService = new CryptographGenerationService(databaseManager);
            scanningService = new CryptographScanningService(databaseManager);
            comparisonService = new BiometricComparisonService(databaseManager);
            historyService = new HistoryAndAuditService(databaseManager);
            
            logger.info("Test environment setup completed");
            
        } catch (Exception e) {
            logger.error("Failed to setup test environment", e);
            fail("Test setup failed: " + e.getMessage());
        }
    }
    
    @AfterEach
    void tearDown() {
        try {
            if (generationService != null) {
                generationService.cleanup();
            }
            
            if (databaseManager != null) {
                databaseManager.close();
            }
            
            logger.info("Test environment cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error during test cleanup", e);
        }
    }
    
    @Test
    void testDatabaseInitialization() {
        logger.info("Testing database initialization...");
        
        assertNotNull(databaseManager, "Database manager should be initialized");
        assertNotNull(databaseManager.getConnection(), "Database connection should be available");
        
        logger.info("Database initialization test passed");
    }
    
    @Test
    void testServiceInitialization() {
        logger.info("Testing service initialization...");
        
        assertNotNull(generationService, "Generation service should be initialized");
        assertNotNull(scanningService, "Scanning service should be initialized");
        assertNotNull(comparisonService, "Comparison service should be initialized");
        assertNotNull(historyService, "History service should be initialized");
        
        logger.info("Service initialization test passed");
    }
    
    @Test
    void testLibraryVersions() {
        logger.info("Testing library version retrieval...");
        
        String versions = generationService.getLibraryVersions();
        assertNotNull(versions, "Library versions should not be null");
        assertFalse(versions.isEmpty(), "Library versions should not be empty");
        
        logger.info("Library versions: {}", versions);
        logger.info("Library version test passed");
    }
    
    @Test
    void testDemographicDataService() {
        logger.info("Testing demographic data service...");
        
        try {
            DemographicDataService demographicService = new DemographicDataService(databaseManager);
            
            // Create test demographic info
            DemographicInfo testInfo = new DemographicInfo();
            testInfo.setFirstName("John");
            testInfo.setLastName("Doe");
            testInfo.setDateOfBirth(LocalDate.of(1990, 1, 1));
            testInfo.setGender("Male");
            testInfo.setNationality("US");
            testInfo.setDocumentNumber("TEST123456");
            testInfo.setDocumentType("Passport");
            
            // Save demographic info
            DemographicInfo saved = demographicService.saveDemographicInfo(testInfo);
            assertNotNull(saved, "Saved demographic info should not be null");
            assertNotNull(saved.getId(), "Saved demographic info should have an ID");
            
            // Retrieve demographic info
            DemographicInfo retrieved = demographicService.getDemographicInfoById(saved.getId());
            assertNotNull(retrieved, "Retrieved demographic info should not be null");
            assertEquals("John", retrieved.getFirstName(), "First name should match");
            assertEquals("Doe", retrieved.getLastName(), "Last name should match");
            
            logger.info("Demographic data service test passed");
            
        } catch (Exception e) {
            logger.error("Demographic data service test failed", e);
            fail("Demographic data service test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testHistoryService() {
        logger.info("Testing history service...");
        
        try {
            // Test system statistics
            HistoryAndAuditService.SystemStatistics stats = historyService.getSystemStatistics();
            assertNotNull(stats, "System statistics should not be null");
            
            // Test generation history
            List<HistoryAndAuditService.GenerationHistoryRecord> genHistory = historyService.getGenerationHistory();
            assertNotNull(genHistory, "Generation history should not be null");
            
            // Test scan history
            List<HistoryAndAuditService.ScanHistoryRecord> scanHistory = historyService.getScanHistory();
            assertNotNull(scanHistory, "Scan history should not be null");
            
            // Test comparison history
            List<HistoryAndAuditService.ComparisonHistoryRecord> compHistory = historyService.getComparisonHistory();
            assertNotNull(compHistory, "Comparison history should not be null");
            
            // Test audit log
            List<HistoryAndAuditService.AuditLogRecord> auditLog = historyService.getAuditLog();
            assertNotNull(auditLog, "Audit log should not be null");
            
            logger.info("History service test passed");
            
        } catch (Exception e) {
            logger.error("History service test failed", e);
            fail("History service test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testBiometricDataService() {
        logger.info("Testing biometric data service...");
        
        try {
            BiometricDataService biometricService = new BiometricDataService(databaseManager);
            
            // Test statistics
            BiometricDataService.BiometricStats stats = biometricService.getBiometricStatistics();
            assertNotNull(stats, "Biometric statistics should not be null");
            
            logger.info("Biometric data service test passed");
            
        } catch (Exception e) {
            logger.error("Biometric data service test failed", e);
            fail("Biometric data service test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testCryptographDAO() {
        logger.info("Testing cryptograph DAO...");
        
        try {
            CryptographDAO cryptographDAO = new CryptographDAO(databaseManager);
            
            // Test getting all cryptographs (should be empty initially)
            List<com.airsnap.cryptograph.model.Cryptograph> cryptographs = cryptographDAO.getAllCryptographs();
            assertNotNull(cryptographs, "Cryptographs list should not be null");
            
            logger.info("Found {} existing cryptographs", cryptographs.size());
            logger.info("Cryptograph DAO test passed");
            
        } catch (Exception e) {
            logger.error("Cryptograph DAO test failed", e);
            fail("Cryptograph DAO test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testMockCryptographGeneration() {
        logger.info("Testing mock cryptograph generation...");
        
        try {
            // Create test demographic info
            DemographicInfo testInfo = new DemographicInfo();
            testInfo.setFirstName("Jane");
            testInfo.setLastName("Smith");
            testInfo.setDateOfBirth(LocalDate.of(1985, 5, 15));
            testInfo.setGender("Female");
            testInfo.setNationality("CA");
            testInfo.setDocumentNumber("TEST789012");
            testInfo.setDocumentType("Passport");
            
            // Create mock biometric data
            byte[] mockFaceImage = "MOCK_FACE_IMAGE_DATA".getBytes();
            byte[] mockFingerprintImage = "MOCK_FINGERPRINT_IMAGE_DATA".getBytes();
            
            // Note: This will likely fail due to missing native libraries, but we can test the service structure
            try {
                com.airsnap.cryptograph.model.Cryptograph result = generationService.generateCryptograph(
                    testInfo, mockFaceImage, mockFingerprintImage);
                
                if (result != null) {
                    assertNotNull(result.getCryptographId(), "Cryptograph ID should not be null");
                    assertNotNull(result.getHash(), "Cryptograph hash should not be null");
                    logger.info("Mock cryptograph generation succeeded: {}", result.getCryptographId());
                } else {
                    logger.info("Mock cryptograph generation returned null (expected due to missing native libraries)");
                }
                
            } catch (Exception e) {
                logger.info("Mock cryptograph generation failed as expected (missing native libraries): {}", e.getMessage());
                // This is expected since we don't have the actual AirSnap native libraries
            }
            
            logger.info("Mock cryptograph generation test completed");
            
        } catch (Exception e) {
            logger.error("Mock cryptograph generation test setup failed", e);
            fail("Mock cryptograph generation test setup failed: " + e.getMessage());
        }
    }
    
    @Test
    void testSystemIntegration() {
        logger.info("Testing system integration...");
        
        // Test that all components can be initialized together
        assertNotNull(databaseManager, "Database manager should be available");
        assertNotNull(generationService, "Generation service should be available");
        assertNotNull(scanningService, "Scanning service should be available");
        assertNotNull(comparisonService, "Comparison service should be available");
        assertNotNull(historyService, "History service should be available");
        
        // Test that services can interact with database
        try {
            // Test database operations through services
            HistoryAndAuditService.SystemStatistics stats = historyService.getSystemStatistics();
            assertNotNull(stats, "Should be able to get system statistics");
            
            logger.info("System integration test passed");
            
        } catch (Exception e) {
            logger.error("System integration test failed", e);
            fail("System integration test failed: " + e.getMessage());
        }
    }
}
