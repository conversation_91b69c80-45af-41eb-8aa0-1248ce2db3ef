import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/biometric_models.dart';
import '../services/airsnap_service.dart';
import '../services/database_service.dart';

class BiometricProvider extends ChangeNotifier {
  // Loading states
  bool _isGenerating = false;
  bool _isScanning = false;
  bool _isComparing = false;
  bool _isLoadingHistory = false;

  // Data
  List<CryptographRecord> _cryptographs = [];
  List<OperationHistory> _operationHistory = [];
  CryptographResult? _lastGeneratedCryptograph;
  ScanResult? _lastScanResult;
  ComparisonResult? _lastComparisonResult;

  // Error handling
  String? _errorMessage;

  // Getters
  bool get isGenerating => _isGenerating;
  bool get isScanning => _isScanning;
  bool get isComparing => _isComparing;
  bool get isLoadingHistory => _isLoadingHistory;
  
  List<CryptographRecord> get cryptographs => _cryptographs;
  List<OperationHistory> get operationHistory => _operationHistory;
  CryptographResult? get lastGeneratedCryptograph => _lastGeneratedCryptograph;
  ScanResult? get lastScanResult => _lastScanResult;
  ComparisonResult? get lastComparisonResult => _lastComparisonResult;
  
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// Generate cryptograph
  Future<bool> generateCryptograph({
    required DemographicData demographicData,
    required String faceImagePath,
    required String fingerprintImagePath,
    required String fingerType,
  }) async {
    _isGenerating = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Validate inputs
      if (!File(faceImagePath).existsSync()) {
        throw Exception('Face image file not found');
      }
      if (!File(fingerprintImagePath).existsSync()) {
        throw Exception('Fingerprint image file not found');
      }

      // Generate cryptograph using AirSnap service
      final result = await AirSnapService.generateCryptograph(
        demographicData: demographicData,
        faceImagePath: faceImagePath,
        fingerprintImagePath: fingerprintImagePath,
        fingerType: fingerType,
      );

      if (result.success) {
        _lastGeneratedCryptograph = result;

        // Save to database
        final record = CryptographRecord(
          cryptographId: result.cryptographId,
          cryptographPath: result.cryptographPath,
          demographicData: demographicData.toJson().toString(),
          fingerType: result.fingerType,
          createdAt: result.generatedAt,
          notes: 'Generated via mobile app',
        );

        await DatabaseService.insertCryptograph(record);
        
        // Refresh cryptographs list
        await loadCryptographs();
        
        _isGenerating = false;
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Failed to generate cryptograph');
        _isGenerating = false;
        return false;
      }
    } catch (e) {
      _setError('Error generating cryptograph: $e');
      _isGenerating = false;
      return false;
    }
  }

  /// Scan cryptograph
  Future<bool> scanCryptograph(String cryptographPath) async {
    _isScanning = true;
    _errorMessage = null;
    notifyListeners();

    try {
      if (!File(cryptographPath).existsSync()) {
        throw Exception('Cryptograph file not found');
      }

      final result = await AirSnapService.scanCryptograph(cryptographPath);

      if (result.success) {
        _lastScanResult = result;
        _isScanning = false;
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Failed to scan cryptograph');
        _isScanning = false;
        return false;
      }
    } catch (e) {
      _setError('Error scanning cryptograph: $e');
      _isScanning = false;
      return false;
    }
  }

  /// Compare fingerprints
  Future<bool> compareFingerprints({
    required String fingerprint1Path,
    required String fingerprint2Path,
  }) async {
    _isComparing = true;
    _errorMessage = null;
    notifyListeners();

    try {
      if (!File(fingerprint1Path).existsSync()) {
        throw Exception('First fingerprint file not found');
      }
      if (!File(fingerprint2Path).existsSync()) {
        throw Exception('Second fingerprint file not found');
      }

      final result = await AirSnapService.compareFingerprints(
        fingerprint1Path: fingerprint1Path,
        fingerprint2Path: fingerprint2Path,
      );

      if (result.success) {
        _lastComparisonResult = result;
        _isComparing = false;
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Failed to compare fingerprints');
        _isComparing = false;
        return false;
      }
    } catch (e) {
      _setError('Error comparing fingerprints: $e');
      _isComparing = false;
      return false;
    }
  }

  /// Load cryptographs from database
  Future<void> loadCryptographs() async {
    try {
      _cryptographs = await DatabaseService.getAllCryptographs();
      notifyListeners();
    } catch (e) {
      _setError('Error loading cryptographs: $e');
    }
  }

  /// Load operation history
  Future<void> loadOperationHistory() async {
    _isLoadingHistory = true;
    notifyListeners();

    try {
      _operationHistory = await DatabaseService.getOperationHistory(limit: 50);
      _isLoadingHistory = false;
      notifyListeners();
    } catch (e) {
      _setError('Error loading operation history: $e');
      _isLoadingHistory = false;
    }
  }

  /// Search cryptographs by name
  Future<void> searchCryptographs(String searchTerm) async {
    try {
      if (searchTerm.isEmpty) {
        await loadCryptographs();
      } else {
        _cryptographs = await DatabaseService.searchCryptographsByName(searchTerm);
        notifyListeners();
      }
    } catch (e) {
      _setError('Error searching cryptographs: $e');
    }
  }

  /// Delete cryptograph
  Future<bool> deleteCryptograph(int id) async {
    try {
      final count = await DatabaseService.deleteCryptograph(id);
      if (count > 0) {
        await loadCryptographs();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Error deleting cryptograph: $e');
      return false;
    }
  }

  /// Get cryptograph by ID
  Future<CryptographRecord?> getCryptographById(String cryptographId) async {
    try {
      return await DatabaseService.getCryptographById(cryptographId);
    } catch (e) {
      _setError('Error getting cryptograph: $e');
      return null;
    }
  }

  /// Validate cryptograph integrity
  Future<ValidationResult?> validateCryptograph(String cryptographPath) async {
    try {
      return await AirSnapService.validateCryptographIntegrity(cryptographPath);
    } catch (e) {
      _setError('Error validating cryptograph: $e');
      return null;
    }
  }

  /// Extract face template
  Future<TemplateResult?> extractFaceTemplate(String facePath) async {
    try {
      return await AirSnapService.extractFaceTemplate(facePath);
    } catch (e) {
      _setError('Error extracting face template: $e');
      return null;
    }
  }

  /// Extract fingerprint template
  Future<FingerprintTemplateResult?> extractFingerprintTemplate(String fingerprintPath) async {
    try {
      return await AirSnapService.extractFingerprintTemplate(fingerprintPath);
    } catch (e) {
      _setError('Error extracting fingerprint template: $e');
      return null;
    }
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    try {
      return await DatabaseService.getDatabaseStats();
    } catch (e) {
      _setError('Error getting database stats: $e');
      return {'cryptographs': 0, 'operations': 0};
    }
  }

  /// Clear all data (for testing purposes)
  Future<void> clearAllData() async {
    try {
      // This would require additional database methods
      _cryptographs.clear();
      _operationHistory.clear();
      _lastGeneratedCryptograph = null;
      _lastScanResult = null;
      _lastComparisonResult = null;
      notifyListeners();
    } catch (e) {
      _setError('Error clearing data: $e');
    }
  }

  /// Refresh all data
  Future<void> refreshAllData() async {
    await Future.wait([
      loadCryptographs(),
      loadOperationHistory(),
    ]);
  }
}
