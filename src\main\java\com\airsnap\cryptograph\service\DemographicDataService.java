package com.airsnap.cryptograph.service;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.DemographicInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for managing demographic information
 */
public class DemographicDataService {
    private static final Logger logger = LoggerFactory.getLogger(DemographicDataService.class);
    private final DatabaseManager dbManager;
    
    public DemographicDataService(DatabaseManager dbManager) {
        this.dbManager = dbManager;
    }
    
    /**
     * Save demographic information to database
     */
    public Long saveDemographicInfo(DemographicInfo demographicInfo) throws SQLException {
        String sql = """
            INSERT INTO demographic_info (first_name, last_name, date_of_birth, gender, 
                                        nationality, document_number, document_type)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            stmt.setString(1, demographicInfo.getFirstName());
            stmt.setString(2, demographicInfo.getLastName());
            stmt.setDate(3, demographicInfo.getDateOfBirth() != null ? 
                        Date.valueOf(demographicInfo.getDateOfBirth()) : null);
            stmt.setString(4, demographicInfo.getGender());
            stmt.setString(5, demographicInfo.getNationality());
            stmt.setString(6, demographicInfo.getDocumentNumber());
            stmt.setString(7, demographicInfo.getDocumentType());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating demographic info failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    Long id = generatedKeys.getLong(1);
                    demographicInfo.setId(id);
                    demographicInfo.setCreatedAt(LocalDateTime.now());
                    
                    // Log audit
                    dbManager.logAudit("CREATE", "DEMOGRAPHIC", id.toString(), 
                                     "SYSTEM", "Demographic info created", true, null);
                    
                    logger.info("Demographic info saved with ID: {} for {}", id, demographicInfo.getFullName());
                    return id;
                } else {
                    throw new SQLException("Creating demographic info failed, no ID obtained.");
                }
            }
        }
    }
    
    /**
     * Find demographic information by ID
     */
    public DemographicInfo findById(Long id) throws SQLException {
        String sql = "SELECT * FROM demographic_info WHERE id = ?";
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToDemographicInfo(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Find demographic information by document number
     */
    public DemographicInfo findByDocumentNumber(String documentNumber) throws SQLException {
        String sql = "SELECT * FROM demographic_info WHERE document_number = ?";
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, documentNumber);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToDemographicInfo(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Search demographic information by name
     */
    public List<DemographicInfo> searchByName(String firstName, String lastName) throws SQLException {
        String sql = "SELECT * FROM demographic_info WHERE first_name LIKE ? AND last_name LIKE ?";
        
        List<DemographicInfo> results = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, "%" + firstName + "%");
            stmt.setString(2, "%" + lastName + "%");
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    results.add(mapResultSetToDemographicInfo(rs));
                }
            }
        }
        return results;
    }
    
    /**
     * Get all demographic information
     */
    public List<DemographicInfo> findAll() throws SQLException {
        String sql = "SELECT * FROM demographic_info ORDER BY created_at DESC";
        
        List<DemographicInfo> results = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                results.add(mapResultSetToDemographicInfo(rs));
            }
        }
        return results;
    }
    
    /**
     * Update demographic information
     */
    public boolean updateDemographicInfo(DemographicInfo demographicInfo) throws SQLException {
        String sql = """
            UPDATE demographic_info 
            SET first_name = ?, last_name = ?, date_of_birth = ?, gender = ?, 
                nationality = ?, document_number = ?, document_type = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, demographicInfo.getFirstName());
            stmt.setString(2, demographicInfo.getLastName());
            stmt.setDate(3, demographicInfo.getDateOfBirth() != null ? 
                        Date.valueOf(demographicInfo.getDateOfBirth()) : null);
            stmt.setString(4, demographicInfo.getGender());
            stmt.setString(5, demographicInfo.getNationality());
            stmt.setString(6, demographicInfo.getDocumentNumber());
            stmt.setString(7, demographicInfo.getDocumentType());
            stmt.setLong(8, demographicInfo.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                demographicInfo.setUpdatedAt(LocalDateTime.now());
                
                // Log audit
                dbManager.logAudit("UPDATE", "DEMOGRAPHIC", demographicInfo.getId().toString(), 
                                 "SYSTEM", "Demographic info updated", true, null);
                
                logger.info("Demographic info updated for ID: {}", demographicInfo.getId());
                return true;
            }
        }
        return false;
    }
    
    /**
     * Delete demographic information
     */
    public boolean deleteDemographicInfo(Long id) throws SQLException {
        DemographicInfo demographicInfo = findById(id);
        if (demographicInfo == null) {
            return false;
        }
        
        String sql = "DELETE FROM demographic_info WHERE id = ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            int affectedRows = stmt.executeUpdate();
            
            if (affectedRows > 0) {
                // Log audit
                dbManager.logAudit("DELETE", "DEMOGRAPHIC", id.toString(), 
                                 "SYSTEM", "Demographic info deleted", true, null);
                
                logger.info("Demographic info deleted for ID: {}", id);
                return true;
            }
        }
        return false;
    }
    
    /**
     * Map ResultSet to DemographicInfo object
     */
    private DemographicInfo mapResultSetToDemographicInfo(ResultSet rs) throws SQLException {
        DemographicInfo demographicInfo = new DemographicInfo();
        demographicInfo.setId(rs.getLong("id"));
        demographicInfo.setFirstName(rs.getString("first_name"));
        demographicInfo.setLastName(rs.getString("last_name"));
        demographicInfo.setDateOfBirth(rs.getDate("date_of_birth") != null ? 
                                     rs.getDate("date_of_birth").toLocalDate() : null);
        demographicInfo.setGender(rs.getString("gender"));
        demographicInfo.setNationality(rs.getString("nationality"));
        demographicInfo.setDocumentNumber(rs.getString("document_number"));
        demographicInfo.setDocumentType(rs.getString("document_type"));
        demographicInfo.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            demographicInfo.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return demographicInfo;
    }
}
