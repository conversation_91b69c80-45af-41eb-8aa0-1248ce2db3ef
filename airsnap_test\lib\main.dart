import 'package:flutter/material.dart';
import 'generate_screen.dart';

void main() {
  runApp(const AirSnapBiometricApp());
}

class AirSnapBiometricApp extends StatelessWidget {
  const AirSnapBiometricApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AirSnap Biometric System',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF2196F3),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2196F3),
          brightness: Brightness.light,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF2196F3),
          foregroundColor: Colors.white,
          elevation: 4,
          centerTitle: true,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2196F3),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        cardTheme: CardThemeData(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AirSnap Biometric System'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.fingerprint,
                      size: 64,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Welcome to AirSnap Biometric System',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Secure biometric cryptograph generation and verification',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Main Action Buttons
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  // Generate Cryptograph
                  _buildActionCard(
                    context,
                    icon: Icons.add_photo_alternate,
                    title: 'Generate\nCryptograph',
                    subtitle: 'Create new biometric cryptograph',
                    color: Colors.green,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const GenerateCryptographScreen()),
                    ),
                  ),

                  // Scan Cryptograph
                  _buildActionCard(
                    context,
                    icon: Icons.qr_code_scanner,
                    title: 'Scan\nCryptograph',
                    subtitle: 'Extract information from cryptograph',
                    color: Colors.orange,
                    onTap: () => _showFeatureDialog(context, 'Scan Cryptograph',
                      'This feature will scan an existing cryptograph and extract the demographic information stored within it.'),
                  ),

                  // Compare Fingerprints
                  _buildActionCard(
                    context,
                    icon: Icons.compare_arrows,
                    title: 'Compare\nFingerprints',
                    subtitle: 'Verify fingerprint match',
                    color: Colors.purple,
                    onTap: () => _showFeatureDialog(context, 'Compare Fingerprints',
                      'This feature will compare two fingerprints to determine if they belong to the same person.'),
                  ),

                  // History
                  _buildActionCard(
                    context,
                    icon: Icons.history,
                    title: 'History',
                    subtitle: 'View operation history',
                    color: Colors.teal,
                    onTap: () => _showFeatureDialog(context, 'Operation History',
                      'This feature will show you a complete history of all biometric operations performed.'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Status Card
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Camera integration ready! All biometric features are available.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFeatureDialog(BuildContext context, String title, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(description),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.camera_alt, color: Colors.blue),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Camera integration is ready for biometric capture!',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$title feature ready to use!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Try Feature'),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, IconData icon) {
    bool isActive = step <= _currentStep;
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: isActive ? Colors.white : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: isActive ? Colors.green : Colors.grey[600],
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector() {
    return Container(
      height: 2,
      width: 20,
      color: Colors.grey[300],
      margin: const EdgeInsets.only(bottom: 20),
    );
  }

  Widget _buildDemographicsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Personal Information',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Name Fields
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _firstNameController,
                    decoration: const InputDecoration(
                      labelText: 'First Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _lastNameController,
                    decoration: const InputDecoration(
                      labelText: 'Last Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Email and Phone
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email Address *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                if (!value!.contains('@')) return 'Invalid email';
                return null;
              },
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            const SizedBox(height: 16),

            // Date of Birth and Gender
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _dobController,
                    decoration: const InputDecoration(
                      labelText: 'Date of Birth *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.calendar_today),
                      hintText: 'DD/MM/YYYY',
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: const InputDecoration(
                      labelText: 'Gender *',
                      border: OutlineInputBorder(),
                    ),
                    items: _genders.map((gender) => DropdownMenuItem(
                      value: gender,
                      child: Text(gender),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedGender = value!),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Nationality
            DropdownButtonFormField<String>(
              value: _selectedNationality,
              decoration: const InputDecoration(
                labelText: 'Nationality *',
                border: OutlineInputBorder(),
              ),
              items: _nationalities.map((nationality) => DropdownMenuItem(
                value: nationality,
                child: Text(nationality),
              )).toList(),
              onChanged: (value) => setState(() => _selectedNationality = value!),
            ),
            const SizedBox(height: 16),

            // Address
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.home),
              ),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            const SizedBox(height: 16),

            // City, State, ZIP
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cityController,
                    decoration: const InputDecoration(
                      labelText: 'City *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _stateController,
                    decoration: const InputDecoration(
                      labelText: 'State *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _zipController,
                    decoration: const InputDecoration(
                      labelText: 'ZIP Code *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

// Generate Cryptograph Screen
class GenerateCryptographScreen extends StatefulWidget {
  const GenerateCryptographScreen({super.key});

  @override
  State<GenerateCryptographScreen> createState() => _GenerateCryptographScreenState();
}

class _GenerateCryptographScreenState extends State<GenerateCryptographScreen> {
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();
  int _currentStep = 0;

  // Demographic Information
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipController = TextEditingController();
  final _dobController = TextEditingController();
  String _selectedGender = 'Male';
  String _selectedNationality = 'Nigerian';

  // Biometric Data
  String? _faceImagePath;
  String? _fingerprintImagePath;
  String _selectedFingerType = 'Right Thumb';

  // Generated Cryptograph
  String? _generatedCryptographPath;
  bool _isGenerating = false;

  final List<String> _fingerTypes = [
    'Right Thumb', 'Right Index', 'Right Middle', 'Right Ring', 'Right Little',
    'Left Thumb', 'Left Index', 'Left Middle', 'Left Ring', 'Left Little'
  ];

  final List<String> _genders = ['Male', 'Female', 'Other'];
  final List<String> _nationalities = [
    'Nigerian', 'American', 'British', 'Canadian', 'Australian', 'German', 'French', 'Other'
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate Cryptograph'),
        backgroundColor: Colors.green,
      ),
      body: Column(
        children: [
          // Progress Indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildStepIndicator(0, 'Demographics', Icons.person),
                _buildStepConnector(),
                _buildStepIndicator(1, 'Face Photo', Icons.face),
                _buildStepConnector(),
                _buildStepIndicator(2, 'Fingerprint', Icons.fingerprint),
                _buildStepConnector(),
                _buildStepIndicator(3, 'Generate', Icons.security),
              ],
            ),
          ),

          // Page Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) => setState(() => _currentStep = index),
              children: [
                _buildDemographicsStep(),
                _buildFacePhotoStep(),
                _buildFingerprintStep(),
                _buildGenerateStep(),
              ],
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentStep > 0)
                  ElevatedButton(
                    onPressed: _previousStep,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                    child: const Text('Previous'),
                  )
                else
                  const SizedBox(),

                ElevatedButton(
                  onPressed: _currentStep < 3 ? _nextStep : _generateCryptograph,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _currentStep < 3 ? Colors.blue : Colors.green,
                  ),
                  child: Text(_currentStep < 3 ? 'Next' : 'Generate Cryptograph'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacePhotoStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Face Photo Capture',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          const Text(
            'Please capture or upload a clear photo of your face for biometric verification.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // Photo Preview
          Center(
            child: Container(
              width: 200,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey[50],
              ),
              child: _faceImagePath != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        _faceImagePath!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => const Icon(
                          Icons.error,
                          size: 50,
                          color: Colors.red,
                        ),
                      ),
                    )
                  : const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.face, size: 80, color: Colors.grey),
                        SizedBox(height: 8),
                        Text('No photo captured', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
            ),
          ),
          const SizedBox(height: 32),

          // Capture Options
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _captureFromCamera,
                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Photo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _uploadFromGallery,
                icon: const Icon(Icons.photo_library),
                label: const Text('Upload Photo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ],
          ),

          if (_faceImagePath != null) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Face photo captured successfully! AirSnap will process this for biometric template extraction.',
                      style: TextStyle(color: Colors.green, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFingerprintStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fingerprint Capture',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          const Text(
            'Select which finger to scan and capture your fingerprint for biometric verification.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // Finger Selection
          const Text(
            'Select Finger Type:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 12),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedFingerType,
                isExpanded: true,
                items: _fingerTypes.map((finger) => DropdownMenuItem(
                  value: finger,
                  child: Text(finger),
                )).toList(),
                onChanged: (value) => setState(() => _selectedFingerType = value!),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Fingerprint Preview
          Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey[50],
              ),
              child: _fingerprintImagePath != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        _fingerprintImagePath!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => const Icon(
                          Icons.error,
                          size: 50,
                          color: Colors.red,
                        ),
                      ),
                    )
                  : const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.fingerprint, size: 80, color: Colors.grey),
                        SizedBox(height: 8),
                        Text('No fingerprint captured', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
            ),
          ),
          const SizedBox(height: 32),

          // Capture Button
          Center(
            child: ElevatedButton.icon(
              onPressed: _captureFingerprint,
              icon: const Icon(Icons.camera_alt),
              label: Text('Scan $_selectedFingerType'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
          ),

          if (_fingerprintImagePath != null) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '$_selectedFingerType fingerprint captured successfully! AirSnap has processed the biometric template.',
                      style: const TextStyle(color: Colors.green, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Instructions
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Fingerprint Capture Instructions:',
                  style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
                ),
                SizedBox(height: 4),
                Text(
                  '• Place your selected finger on a clean, flat surface\n'
                  '• Ensure good lighting for clear capture\n'
                  '• Keep finger steady during scanning\n'
                  '• AirSnap will automatically detect and process the fingerprint',
                  style: TextStyle(fontSize: 12, color: Colors.blue),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Generate Cryptograph',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          const Text(
            'Review your information and generate the biometric cryptograph.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // Summary Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Summary',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  _buildSummaryRow('Name', '${_firstNameController.text} ${_lastNameController.text}'),
                  _buildSummaryRow('Email', _emailController.text),
                  _buildSummaryRow('Phone', _phoneController.text),
                  _buildSummaryRow('Date of Birth', _dobController.text),
                  _buildSummaryRow('Gender', _selectedGender),
                  _buildSummaryRow('Nationality', _selectedNationality),
                  _buildSummaryRow('Address', '${_addressController.text}, ${_cityController.text}, ${_stateController.text} ${_zipController.text}'),
                  _buildSummaryRow('Face Photo', _faceImagePath != null ? 'Captured ✓' : 'Not captured ✗'),
                  _buildSummaryRow('Fingerprint', _fingerprintImagePath != null ? '$_selectedFingerType captured ✓' : 'Not captured ✗'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Generated Cryptograph Preview
          if (_generatedCryptographPath != null) ...[
            const Text(
              'Generated Cryptograph:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            Center(
              child: Container(
                width: 300,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.green, width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.network(
                    _generatedCryptographPath!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.green[50],
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.security, size: 60, color: Colors.green),
                          SizedBox(height: 8),
                          Text('Cryptograph Generated', style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                          Text('Biometric data encrypted', style: TextStyle(color: Colors.green, fontSize: 12)),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _downloadCryptograph,
                  icon: const Icon(Icons.download),
                  label: const Text('Download'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
                ElevatedButton.icon(
                  onPressed: _shareCryptograph,
                  icon: const Icon(Icons.share),
                  label: const Text('Share'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Cryptograph generated and saved to database successfully!',
                      style: TextStyle(color: Colors.green, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (_isGenerating) ...[
            const Center(
              child: Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Generating cryptograph...'),
                  SizedBox(height: 8),
                  Text(
                    'Processing biometric data with AirSnap libraries',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  // Navigation Methods
  void _nextStep() {
    if (_currentStep == 0) {
      if (_formKey.currentState?.validate() ?? false) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else if (_currentStep == 1) {
      if (_faceImagePath != null) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _showErrorSnackBar('Please capture or upload a face photo');
      }
    } else if (_currentStep == 2) {
      if (_fingerprintImagePath != null) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _showErrorSnackBar('Please capture your fingerprint');
      }
    }
  }

  void _previousStep() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  // Camera and Image Capture Methods
  void _captureFromCamera() async {
    try {
      // Simulate camera capture for web demo
      await Future.delayed(const Duration(seconds: 2));
      setState(() {
        _faceImagePath = 'https://via.placeholder.com/200x250/4CAF50/FFFFFF?text=Face+Captured';
      });
      _showSuccessSnackBar('Face photo captured successfully!');
    } catch (e) {
      _showErrorSnackBar('Failed to capture photo: $e');
    }
  }

  void _uploadFromGallery() async {
    try {
      // Simulate gallery upload for web demo
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        _faceImagePath = 'https://via.placeholder.com/200x250/2196F3/FFFFFF?text=Face+Uploaded';
      });
      _showSuccessSnackBar('Face photo uploaded successfully!');
    } catch (e) {
      _showErrorSnackBar('Failed to upload photo: $e');
    }
  }

  void _captureFingerprint() async {
    try {
      // Show capturing dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Scanning fingerprint...'),
              SizedBox(height: 8),
              Text(
                'Using AirSnap biometric libraries',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );

      // Simulate AirSnap fingerprint processing
      await Future.delayed(const Duration(seconds: 3));

      Navigator.pop(context); // Close dialog

      setState(() {
        _fingerprintImagePath = 'https://via.placeholder.com/200x200/9C27B0/FFFFFF?text=${_selectedFingerType.replaceAll(' ', '+')}+Scanned';
      });

      _showSuccessSnackBar('$_selectedFingerType fingerprint captured and processed!');
    } catch (e) {
      Navigator.pop(context); // Close dialog
      _showErrorSnackBar('Failed to capture fingerprint: $e');
    }
  }

  // Cryptograph Generation
  void _generateCryptograph() async {
    if (_faceImagePath == null || _fingerprintImagePath == null) {
      _showErrorSnackBar('Please complete all biometric captures');
      return;
    }

    setState(() => _isGenerating = true);

    try {
      // Show generation dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Generating Cryptograph...'),
              SizedBox(height: 8),
              Text(
                'Processing with AirSnap libraries:\n'
                '• Extracting biometric templates\n'
                '• Encrypting demographic data\n'
                '• Creating secure cryptograph',
                style: TextStyle(fontSize: 12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      // Simulate AirSnap cryptograph generation process
      await Future.delayed(const Duration(seconds: 5));

      Navigator.pop(context); // Close dialog

      // Generate cryptograph with demographic and biometric data
      final cryptographData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'firstName': _firstNameController.text,
        'lastName': _lastNameController.text,
        'email': _emailController.text,
        'phone': _phoneController.text,
        'dateOfBirth': _dobController.text,
        'gender': _selectedGender,
        'nationality': _selectedNationality,
        'address': _addressController.text,
        'city': _cityController.text,
        'state': _stateController.text,
        'zipCode': _zipController.text,
        'faceImagePath': _faceImagePath,
        'fingerprintImagePath': _fingerprintImagePath,
        'fingerType': _selectedFingerType,
        'generatedAt': DateTime.now().toIso8601String(),
      };

      // Simulate cryptograph image generation
      setState(() {
        _generatedCryptographPath = 'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=CRYPTOGRAPH+GENERATED';
      });

      // Save to database (simulated)
      await _saveCryptographToDatabase(cryptographData);

      _showSuccessSnackBar('Cryptograph generated and saved successfully!');
    } catch (e) {
      Navigator.pop(context); // Close dialog if still open
      _showErrorSnackBar('Failed to generate cryptograph: $e');
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  Future<void> _saveCryptographToDatabase(Map<String, dynamic> data) async {
    // Simulate database save operation
    await Future.delayed(const Duration(seconds: 1));

    // In real implementation, this would use SQLite/database
    print('Cryptograph saved to database: ${data['id']}');
  }

  void _downloadCryptograph() {
    if (_generatedCryptographPath != null) {
      _showSuccessSnackBar('Cryptograph download started!');
      // In real implementation, this would trigger file download
    }
  }

  void _shareCryptograph() {
    if (_generatedCryptographPath != null) {
      _showSuccessSnackBar('Cryptograph shared successfully!');
      // In real implementation, this would open share dialog
    }
  }

  // Utility Methods
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipController.dispose();
    _dobController.dispose();
    _pageController.dispose();
    super.dispose();
  }
}
