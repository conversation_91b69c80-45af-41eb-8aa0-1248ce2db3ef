import 'package:flutter/material.dart';

void main() {
  runApp(const AirSnapBiometricApp());
}

class AirSnapBiometricApp extends StatelessWidget {
  const AirSnapBiometricApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AirSnap Biometric System',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF2196F3),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2196F3),
          brightness: Brightness.light,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF2196F3),
          foregroundColor: Colors.white,
          elevation: 4,
          centerTitle: true,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2196F3),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        cardTheme: CardThemeData(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AirSnap Biometric System'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.fingerprint,
                      size: 64,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Welcome to AirSnap Biometric System',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Secure biometric cryptograph generation and verification',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Main Action Buttons
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  // Generate Cryptograph
                  _buildActionCard(
                    context,
                    icon: Icons.add_photo_alternate,
                    title: 'Generate\nCryptograph',
                    subtitle: 'Create new biometric cryptograph',
                    color: Colors.green,
                    onTap: () => _showFeatureDialog(context, 'Generate Cryptograph',
                      'This feature will capture your face and fingerprint to generate a secure cryptograph with your demographic information.'),
                  ),

                  // Scan Cryptograph
                  _buildActionCard(
                    context,
                    icon: Icons.qr_code_scanner,
                    title: 'Scan\nCryptograph',
                    subtitle: 'Extract information from cryptograph',
                    color: Colors.orange,
                    onTap: () => _showFeatureDialog(context, 'Scan Cryptograph',
                      'This feature will scan an existing cryptograph and extract the demographic information stored within it.'),
                  ),

                  // Compare Fingerprints
                  _buildActionCard(
                    context,
                    icon: Icons.compare_arrows,
                    title: 'Compare\nFingerprints',
                    subtitle: 'Verify fingerprint match',
                    color: Colors.purple,
                    onTap: () => _showFeatureDialog(context, 'Compare Fingerprints',
                      'This feature will compare two fingerprints to determine if they belong to the same person.'),
                  ),

                  // History
                  _buildActionCard(
                    context,
                    icon: Icons.history,
                    title: 'History',
                    subtitle: 'View operation history',
                    color: Colors.teal,
                    onTap: () => _showFeatureDialog(context, 'Operation History',
                      'This feature will show you a complete history of all biometric operations performed.'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Status Card
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Camera integration ready! All biometric features are available.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFeatureDialog(BuildContext context, String title, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(description),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.camera_alt, color: Colors.blue),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Camera integration is ready for biometric capture!',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$title feature ready to use!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Try Feature'),
          ),
        ],
      ),
    );
  }
}
