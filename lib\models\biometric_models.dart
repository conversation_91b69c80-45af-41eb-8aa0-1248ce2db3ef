import 'dart:convert';

/// Demographic data for cryptograph generation
class DemographicData {
  final String firstName;
  final String lastName;
  final String dateOfBirth;
  final String nationality;
  final String gender;
  final String documentNumber;
  final String documentType;
  final String issuingCountry;
  final String expiryDate;

  DemographicData({
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
    required this.nationality,
    required this.gender,
    this.documentNumber = '',
    this.documentType = '',
    this.issuingCountry = '',
    this.expiryDate = '',
  });

  factory DemographicData.empty() {
    return DemographicData(
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      nationality: '',
      gender: '',
    );
  }

  factory DemographicData.fromJson(Map<String, dynamic> json) {
    return DemographicData(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      nationality: json['nationality'] ?? '',
      gender: json['gender'] ?? '',
      documentNumber: json['documentNumber'] ?? '',
      documentType: json['documentType'] ?? '',
      issuingCountry: json['issuingCountry'] ?? '',
      expiryDate: json['expiryDate'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth,
      'nationality': nationality,
      'gender': gender,
      'documentNumber': documentNumber,
      'documentType': documentType,
      'issuingCountry': issuingCountry,
      'expiryDate': expiryDate,
    };
  }

  String get fullName => '$firstName $lastName';
}

/// Result of AirSnap initialization
class InitializationResult {
  final bool success;
  final String message;
  final String version;
  final List<String> libraries;

  InitializationResult({
    required this.success,
    required this.message,
    required this.version,
    required this.libraries,
  });
}

/// Result of cryptograph generation
class CryptographResult {
  final bool success;
  final String cryptographId;
  final String cryptographPath;
  final int faceTemplateSize;
  final int fingerprintTemplateSize;
  final String fingerType;
  final String generatedAt;
  final String? errorMessage;

  CryptographResult({
    required this.success,
    required this.cryptographId,
    required this.cryptographPath,
    required this.faceTemplateSize,
    required this.fingerprintTemplateSize,
    required this.fingerType,
    required this.generatedAt,
    this.errorMessage,
  });
}

/// Result of cryptograph scanning
class ScanResult {
  final bool success;
  final DemographicData demographicData;
  final String cryptographId;
  final String fingerType;
  final bool faceTemplatePresent;
  final bool fingerprintTemplatePresent;
  final String scannedAt;
  final String? errorMessage;

  ScanResult({
    required this.success,
    required this.demographicData,
    required this.cryptographId,
    required this.fingerType,
    required this.faceTemplatePresent,
    required this.fingerprintTemplatePresent,
    required this.scannedAt,
    this.errorMessage,
  });
}

/// Result of fingerprint comparison
class ComparisonResult {
  final bool success;
  final bool isMatch;
  final double matchScore;
  final double threshold;
  final String confidence;
  final String comparedAt;
  final String? errorMessage;

  ComparisonResult({
    required this.success,
    required this.isMatch,
    required this.matchScore,
    required this.threshold,
    required this.confidence,
    required this.comparedAt,
    this.errorMessage,
  });
}

/// Result of template extraction
class TemplateResult {
  final bool success;
  final int templateSize;
  final String quality;
  final String extractedAt;
  final String? errorMessage;

  TemplateResult({
    required this.success,
    required this.templateSize,
    required this.quality,
    required this.extractedAt,
    this.errorMessage,
  });
}

/// Result of fingerprint template extraction
class FingerprintTemplateResult extends TemplateResult {
  final int minutiaeCount;

  FingerprintTemplateResult({
    required bool success,
    required int templateSize,
    required this.minutiaeCount,
    required String quality,
    required String extractedAt,
    String? errorMessage,
  }) : super(
          success: success,
          templateSize: templateSize,
          quality: quality,
          extractedAt: extractedAt,
          errorMessage: errorMessage,
        );
}

/// Result of cryptograph validation
class ValidationResult {
  final bool success;
  final bool isValid;
  final double integrityScore;
  final String validatedAt;
  final String? errorMessage;

  ValidationResult({
    required this.success,
    required this.isValid,
    required this.integrityScore,
    required this.validatedAt,
    this.errorMessage,
  });
}

/// Result of metadata extraction
class MetadataResult {
  final bool success;
  final String fileName;
  final int fileSize;
  final String lastModified;
  final String path;
  final String? errorMessage;

  MetadataResult({
    required this.success,
    required this.fileName,
    required this.fileSize,
    required this.lastModified,
    required this.path,
    this.errorMessage,
  });
}

/// Cryptograph record for database storage
class CryptographRecord {
  final int? id;
  final String cryptographId;
  final String cryptographPath;
  final String demographicData;
  final String fingerType;
  final String createdAt;
  final String? notes;

  CryptographRecord({
    this.id,
    required this.cryptographId,
    required this.cryptographPath,
    required this.demographicData,
    required this.fingerType,
    required this.createdAt,
    this.notes,
  });

  factory CryptographRecord.fromMap(Map<String, dynamic> map) {
    return CryptographRecord(
      id: map['id'],
      cryptographId: map['cryptographId'],
      cryptographPath: map['cryptographPath'],
      demographicData: map['demographicData'],
      fingerType: map['fingerType'],
      createdAt: map['createdAt'],
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cryptographId': cryptographId,
      'cryptographPath': cryptographPath,
      'demographicData': demographicData,
      'fingerType': fingerType,
      'createdAt': createdAt,
      'notes': notes,
    };
  }

  DemographicData get parsedDemographicData {
    try {
      return DemographicData.fromJson(jsonDecode(demographicData));
    } catch (e) {
      return DemographicData.empty();
    }
  }
}

/// Operation history record
class OperationHistory {
  final int? id;
  final String operationType;
  final String operationData;
  final String result;
  final String timestamp;
  final bool success;

  OperationHistory({
    this.id,
    required this.operationType,
    required this.operationData,
    required this.result,
    required this.timestamp,
    required this.success,
  });

  factory OperationHistory.fromMap(Map<String, dynamic> map) {
    return OperationHistory(
      id: map['id'],
      operationType: map['operationType'],
      operationData: map['operationData'],
      result: map['result'],
      timestamp: map['timestamp'],
      success: map['success'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'operationType': operationType,
      'operationData': operationData,
      'result': result,
      'timestamp': timestamp,
      'success': success ? 1 : 0,
    };
  }
}

/// Finger types enum
enum FingerType {
  rightThumb('Right Thumb'),
  rightIndex('Right Index'),
  rightMiddle('Right Middle'),
  rightRing('Right Ring'),
  rightLittle('Right Little'),
  leftThumb('Left Thumb'),
  leftIndex('Left Index'),
  leftMiddle('Left Middle'),
  leftRing('Left Ring'),
  leftLittle('Left Little');

  const FingerType(this.displayName);
  final String displayName;
}

/// Gender enum
enum Gender {
  male('Male'),
  female('Female'),
  other('Other');

  const Gender(this.displayName);
  final String displayName;
}
