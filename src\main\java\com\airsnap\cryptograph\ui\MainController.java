package com.airsnap.cryptograph.ui;

import com.airsnap.cryptograph.model.Cryptograph;
import com.airsnap.cryptograph.model.DemographicInfo;
import com.airsnap.cryptograph.service.*;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.ResourceBundle;

/**
 * Main controller for the AirSnap Cryptograph System UI
 */
public class MainController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    // Services
    private CryptographGenerationService generationService;
    private CryptographScanningService scanningService;
    private BiometricComparisonService comparisonService;
    private HistoryAndAuditService historyService;

    // Main UI Components
    @FXML private TabPane mainTabPane;

    // Generate Cryptograph Tab
    @FXML private TextField firstNameField;
    @FXML private TextField lastNameField;
    @FXML private DatePicker dateOfBirthPicker;
    @FXML private ComboBox<String> genderComboBox;
    @FXML private TextField nationalityField;
    @FXML private TextField documentNumberField;
    @FXML private ComboBox<String> documentTypeComboBox;
    @FXML private ImageView faceImageView;
    @FXML private ImageView fingerprintImageView;
    @FXML private Button selectFaceImageButton;
    @FXML private Button selectFingerprintImageButton;
    @FXML private Button generateCryptographButton;
    @FXML private TextArea generationResultArea;
    @FXML private ProgressBar generationProgressBar;

    // Scan Cryptograph Tab
    @FXML private Button selectCryptographFileButton;
    @FXML private Label selectedCryptographLabel;
    @FXML private Button scanCryptographButton;
    @FXML private TextArea scanResultArea;
    @FXML private ProgressBar scanProgressBar;
    @FXML private TableView<ScanResultTableItem> scanResultTable;

    // Compare Biometrics Tab
    @FXML private Button selectFirstBiometricButton;
    @FXML private Button selectSecondBiometricButton;
    @FXML private Label firstBiometricLabel;
    @FXML private Label secondBiometricLabel;
    @FXML private ComboBox<String> comparisonTypeComboBox;
    @FXML private Button performComparisonButton;
    @FXML private TextArea comparisonResultArea;
    @FXML private ProgressBar comparisonProgressBar;

    // History Tab
    @FXML private TableView<HistoryTableItem> historyTable;
    @FXML private ComboBox<String> historyTypeComboBox;
    @FXML private DatePicker historyStartDatePicker;
    @FXML private DatePicker historyEndDatePicker;
    @FXML private Button refreshHistoryButton;
    @FXML private TextArea historyDetailsArea;

    // File data
    private byte[] selectedFaceImageData;
    private byte[] selectedFingerprintImageData;
    private byte[] selectedCryptographData;
    private byte[] firstBiometricData;
    private byte[] secondBiometricData;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("Initializing MainController...");

        // Initialize combo boxes
        initializeComboBoxes();

        // Initialize progress bars
        initializeProgressBars();

        // Initialize event handlers
        initializeEventHandlers();

        logger.info("MainController initialized successfully");
    }

    /**
     * Initialize services (called from main application)
     */
    public void initializeServices(CryptographGenerationService generationService,
                                 CryptographScanningService scanningService,
                                 BiometricComparisonService comparisonService,
                                 HistoryAndAuditService historyService) {
        this.generationService = generationService;
        this.scanningService = scanningService;
        this.comparisonService = comparisonService;
        this.historyService = historyService;

        logger.info("Services injected into MainController");

        // Load initial data
        loadInitialData();
    }

    /**
     * Initialize combo boxes with default values
     */
    private void initializeComboBoxes() {
        // Gender combo box
        genderComboBox.getItems().addAll("Male", "Female", "Other");

        // Document type combo box
        documentTypeComboBox.getItems().addAll(
            "Passport", "National ID", "Driver's License", "Residence Permit", "Other"
        );

        // Comparison type combo box
        comparisonTypeComboBox.getItems().addAll(
            "Fingerprint Templates", "Face Templates", "Complete Cryptographs"
        );
        comparisonTypeComboBox.setValue("Fingerprint Templates");

        // History type combo box
        historyTypeComboBox.getItems().addAll(
            "All History", "Generation History", "Scan History", "Comparison History", "Audit Log"
        );
        historyTypeComboBox.setValue("All History");
    }

    /**
     * Initialize progress bars
     */
    private void initializeProgressBars() {
        generationProgressBar.setVisible(false);
        scanProgressBar.setVisible(false);
        comparisonProgressBar.setVisible(false);
    }

    /**
     * Initialize event handlers
     */
    private void initializeEventHandlers() {
        // Generate Cryptograph Tab
        selectFaceImageButton.setOnAction(e -> selectFaceImage());
        selectFingerprintImageButton.setOnAction(e -> selectFingerprintImage());
        generateCryptographButton.setOnAction(e -> generateCryptograph());

        // Scan Cryptograph Tab
        selectCryptographFileButton.setOnAction(e -> selectCryptographFile());
        scanCryptographButton.setOnAction(e -> scanCryptograph());

        // Compare Biometrics Tab
        selectFirstBiometricButton.setOnAction(e -> selectFirstBiometric());
        selectSecondBiometricButton.setOnAction(e -> selectSecondBiometric());
        performComparisonButton.setOnAction(e -> performComparison());

        // History Tab
        refreshHistoryButton.setOnAction(e -> refreshHistory());
        historyTypeComboBox.setOnAction(e -> refreshHistory());
    }

    /**
     * Load initial data
     */
    private void loadInitialData() {
        // Load history data
        refreshHistory();

        // Display library version information
        if (generationService != null) {
            String versionInfo = generationService.getLibraryVersions();
            Platform.runLater(() -> {
                generationResultArea.setText("AirSnap Cryptograph System Ready\n\n" +
                                           "Library Information:\n" + versionInfo);
            });
        }
    }

    // ===== GENERATE CRYPTOGRAPH TAB METHODS =====

    /**
     * Select face image file
     */
    private void selectFaceImage() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Face Image");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.gif")
        );

        File selectedFile = fileChooser.showOpenDialog(selectFaceImageButton.getScene().getWindow());
        if (selectedFile != null) {
            try {
                selectedFaceImageData = Files.readAllBytes(selectedFile.toPath());

                // Display image
                Image image = new Image(selectedFile.toURI().toString());
                faceImageView.setImage(image);
                faceImageView.setFitWidth(150);
                faceImageView.setFitHeight(150);
                faceImageView.setPreserveRatio(true);

                logger.info("Face image selected: {} ({} bytes)", selectedFile.getName(), selectedFaceImageData.length);

            } catch (IOException e) {
                logger.error("Error reading face image file", e);
                showAlert(Alert.AlertType.ERROR, "File Error", "Failed to read face image file: " + e.getMessage());
            }
        }
    }

    /**
     * Select fingerprint image file
     */
    private void selectFingerprintImage() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Fingerprint Image");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.gif")
        );

        File selectedFile = fileChooser.showOpenDialog(selectFingerprintImageButton.getScene().getWindow());
        if (selectedFile != null) {
            try {
                selectedFingerprintImageData = Files.readAllBytes(selectedFile.toPath());

                // Display image
                Image image = new Image(selectedFile.toURI().toString());
                fingerprintImageView.setImage(image);
                fingerprintImageView.setFitWidth(150);
                fingerprintImageView.setFitHeight(150);
                fingerprintImageView.setPreserveRatio(true);

                logger.info("Fingerprint image selected: {} ({} bytes)", selectedFile.getName(), selectedFingerprintImageData.length);

            } catch (IOException e) {
                logger.error("Error reading fingerprint image file", e);
                showAlert(Alert.AlertType.ERROR, "File Error", "Failed to read fingerprint image file: " + e.getMessage());
            }
        }
    }

    /**
     * Generate cryptograph from form data
     */
    private void generateCryptograph() {
        // Validate form data
        if (!validateGenerationForm()) {
            return;
        }

        // Create demographic info from form
        DemographicInfo demographicInfo = createDemographicInfoFromForm();

        // Show progress
        generationProgressBar.setVisible(true);
        generateCryptographButton.setDisable(true);
        generationResultArea.setText("Generating cryptograph...");

        // Create background task
        Task<Cryptograph> task = new Task<Cryptograph>() {
            @Override
            protected Cryptograph call() throws Exception {
                return generationService.generateCryptograph(demographicInfo, selectedFaceImageData, selectedFingerprintImageData);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    Cryptograph result = getValue();
                    displayGenerationResult(result);
                    generationProgressBar.setVisible(false);
                    generateCryptographButton.setDisable(false);
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Cryptograph generation failed", exception);
                    generationResultArea.setText("Generation failed: " + exception.getMessage());
                    generationProgressBar.setVisible(false);
                    generateCryptographButton.setDisable(false);
                    showAlert(Alert.AlertType.ERROR, "Generation Error", "Failed to generate cryptograph: " + exception.getMessage());
                });
            }
        };

        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }

    /**
     * Validate generation form data
     */
    private boolean validateGenerationForm() {
        if (firstNameField.getText().trim().isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "First name is required");
            return false;
        }

        if (lastNameField.getText().trim().isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Last name is required");
            return false;
        }

        if (dateOfBirthPicker.getValue() == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Date of birth is required");
            return false;
        }

        if (genderComboBox.getValue() == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Gender is required");
            return false;
        }

        if (selectedFaceImageData == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Face image is required");
            return false;
        }

        if (selectedFingerprintImageData == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Fingerprint image is required");
            return false;
        }

        return true;
    }

    /**
     * Create demographic info from form data
     */
    private DemographicInfo createDemographicInfoFromForm() {
        DemographicInfo info = new DemographicInfo();
        info.setFirstName(firstNameField.getText().trim());
        info.setLastName(lastNameField.getText().trim());
        info.setDateOfBirth(dateOfBirthPicker.getValue());
        info.setGender(genderComboBox.getValue());
        info.setNationality(nationalityField.getText().trim());
        info.setDocumentNumber(documentNumberField.getText().trim());
        info.setDocumentType(documentTypeComboBox.getValue());
        return info;
    }

    /**
     * Display generation result
     */
    private void displayGenerationResult(Cryptograph cryptograph) {
        if (cryptograph != null) {
            StringBuilder result = new StringBuilder();
            result.append("Cryptograph Generated Successfully!\n\n");
            result.append("Cryptograph ID: ").append(cryptograph.getCryptographId()).append("\n");
            result.append("Generation Method: ").append(cryptograph.getGenerationMethod()).append("\n");
            result.append("Data Size: ").append(cryptograph.getCryptographData().length).append(" bytes\n");
            result.append("Hash: ").append(cryptograph.getHash()).append("\n");
            result.append("Created: ").append(cryptograph.getCreatedAt()).append("\n");

            generationResultArea.setText(result.toString());

            // Clear form
            clearGenerationForm();

            // Refresh history
            refreshHistory();

        } else {
            generationResultArea.setText("Generation failed: No cryptograph returned");
        }
    }

    /**
     * Clear generation form
     */
    private void clearGenerationForm() {
        firstNameField.clear();
        lastNameField.clear();
        dateOfBirthPicker.setValue(null);
        genderComboBox.setValue(null);
        nationalityField.clear();
        documentNumberField.clear();
        documentTypeComboBox.setValue(null);
        faceImageView.setImage(null);
        fingerprintImageView.setImage(null);
        selectedFaceImageData = null;
        selectedFingerprintImageData = null;
    }

    // ===== SCAN CRYPTOGRAPH TAB METHODS =====

    /**
     * Select cryptograph file to scan
     */
    private void selectCryptographFile() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Cryptograph File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("All Files", "*.*"),
            new FileChooser.ExtensionFilter("Binary Files", "*.bin", "*.dat")
        );

        File selectedFile = fileChooser.showOpenDialog(selectCryptographFileButton.getScene().getWindow());
        if (selectedFile != null) {
            try {
                selectedCryptographData = Files.readAllBytes(selectedFile.toPath());
                selectedCryptographLabel.setText("Selected: " + selectedFile.getName() +
                                                " (" + selectedCryptographData.length + " bytes)");

                logger.info("Cryptograph file selected: {} ({} bytes)", selectedFile.getName(), selectedCryptographData.length);

            } catch (IOException e) {
                logger.error("Error reading cryptograph file", e);
                showAlert(Alert.AlertType.ERROR, "File Error", "Failed to read cryptograph file: " + e.getMessage());
            }
        }
    }

    /**
     * Scan selected cryptograph
     */
    private void scanCryptograph() {
        if (selectedCryptographData == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Please select a cryptograph file first");
            return;
        }

        // Show progress
        scanProgressBar.setVisible(true);
        scanCryptographButton.setDisable(true);
        scanResultArea.setText("Scanning cryptograph...");

        // Create background task
        Task<CryptographScanningService.ScanResult> task = new Task<CryptographScanningService.ScanResult>() {
            @Override
            protected CryptographScanningService.ScanResult call() throws Exception {
                return scanningService.scanCryptograph(selectedCryptographData);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    CryptographScanningService.ScanResult result = getValue();
                    displayScanResult(result);
                    scanProgressBar.setVisible(false);
                    scanCryptographButton.setDisable(false);
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Cryptograph scanning failed", exception);
                    scanResultArea.setText("Scanning failed: " + exception.getMessage());
                    scanProgressBar.setVisible(false);
                    scanCryptographButton.setDisable(false);
                    showAlert(Alert.AlertType.ERROR, "Scanning Error", "Failed to scan cryptograph: " + exception.getMessage());
                });
            }
        };

        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }

    /**
     * Display scan result
     */
    private void displayScanResult(CryptographScanningService.ScanResult scanResult) {
        if (scanResult != null) {
            StringBuilder result = new StringBuilder();
            result.append("Cryptograph Scan Result\n\n");
            result.append("Scan Status: ").append(scanResult.isSuccess() ? "SUCCESS" : "FAILED").append("\n");
            result.append("Confidence: ").append(String.format("%.2f%%", scanResult.getConfidence() * 100)).append("\n");
            result.append("Message: ").append(scanResult.getMessage()).append("\n\n");

            if (scanResult.getDemographicInfo() != null) {
                DemographicInfo demo = scanResult.getDemographicInfo();
                result.append("Extracted Demographic Information:\n");
                result.append("Name: ").append(demo.getFirstName()).append(" ").append(demo.getLastName()).append("\n");
                result.append("Date of Birth: ").append(demo.getDateOfBirth()).append("\n");
                result.append("Gender: ").append(demo.getGender()).append("\n");
                result.append("Nationality: ").append(demo.getNationality()).append("\n");
                result.append("Document: ").append(demo.getDocumentType()).append(" - ").append(demo.getDocumentNumber()).append("\n");
            }

            scanResultArea.setText(result.toString());

            // Refresh history
            refreshHistory();

        } else {
            scanResultArea.setText("Scanning failed: No result returned");
        }
    }

    // ===== COMPARE BIOMETRICS TAB METHODS =====

    /**
     * Select first biometric file
     */
    private void selectFirstBiometric() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select First Biometric File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("All Files", "*.*"),
            new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.gif"),
            new FileChooser.ExtensionFilter("Binary Files", "*.bin", "*.dat")
        );

        File selectedFile = fileChooser.showOpenDialog(selectFirstBiometricButton.getScene().getWindow());
        if (selectedFile != null) {
            try {
                firstBiometricData = Files.readAllBytes(selectedFile.toPath());
                firstBiometricLabel.setText("First: " + selectedFile.getName() +
                                          " (" + firstBiometricData.length + " bytes)");

                logger.info("First biometric file selected: {} ({} bytes)", selectedFile.getName(), firstBiometricData.length);

            } catch (IOException e) {
                logger.error("Error reading first biometric file", e);
                showAlert(Alert.AlertType.ERROR, "File Error", "Failed to read first biometric file: " + e.getMessage());
            }
        }
    }

    /**
     * Select second biometric file
     */
    private void selectSecondBiometric() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Second Biometric File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("All Files", "*.*"),
            new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.gif"),
            new FileChooser.ExtensionFilter("Binary Files", "*.bin", "*.dat")
        );

        File selectedFile = fileChooser.showOpenDialog(selectSecondBiometricButton.getScene().getWindow());
        if (selectedFile != null) {
            try {
                secondBiometricData = Files.readAllBytes(selectedFile.toPath());
                secondBiometricLabel.setText("Second: " + selectedFile.getName() +
                                           " (" + secondBiometricData.length + " bytes)");

                logger.info("Second biometric file selected: {} ({} bytes)", selectedFile.getName(), secondBiometricData.length);

            } catch (IOException e) {
                logger.error("Error reading second biometric file", e);
                showAlert(Alert.AlertType.ERROR, "File Error", "Failed to read second biometric file: " + e.getMessage());
            }
        }
    }

    /**
     * Perform biometric comparison
     */
    private void performComparison() {
        if (firstBiometricData == null || secondBiometricData == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Please select both biometric files first");
            return;
        }

        String comparisonType = comparisonTypeComboBox.getValue();
        if (comparisonType == null) {
            showAlert(Alert.AlertType.WARNING, "Validation Error", "Please select comparison type");
            return;
        }

        // Show progress
        comparisonProgressBar.setVisible(true);
        performComparisonButton.setDisable(true);
        comparisonResultArea.setText("Performing " + comparisonType.toLowerCase() + " comparison...");

        // Create background task
        Task<String> task = new Task<String>() {
            @Override
            protected String call() throws Exception {
                switch (comparisonType) {
                    case "Fingerprint Templates":
                        BiometricComparisonService.FingerprintComparisonResult fingerprintResult =
                            comparisonService.compareFingerprintTemplates(firstBiometricData, secondBiometricData, "file1", "file2");
                        return formatComparisonResult(fingerprintResult);

                    case "Face Templates":
                        BiometricComparisonService.FaceComparisonResult faceResult =
                            comparisonService.compareFaceTemplates(firstBiometricData, secondBiometricData, "file1", "file2");
                        return formatComparisonResult(faceResult);

                    case "Complete Cryptographs":
                        BiometricComparisonService.CryptographComparisonResult cryptographResult =
                            comparisonService.compareCryptographs(firstBiometricData, secondBiometricData, "file1", "file2");
                        return formatComparisonResult(cryptographResult);

                    default:
                        return "Unknown comparison type: " + comparisonType;
                }
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    String result = getValue();
                    comparisonResultArea.setText(result);
                    comparisonProgressBar.setVisible(false);
                    performComparisonButton.setDisable(false);

                    // Refresh history
                    refreshHistory();
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Biometric comparison failed", exception);
                    comparisonResultArea.setText("Comparison failed: " + exception.getMessage());
                    comparisonProgressBar.setVisible(false);
                    performComparisonButton.setDisable(false);
                    showAlert(Alert.AlertType.ERROR, "Comparison Error", "Failed to perform comparison: " + exception.getMessage());
                });
            }
        };

        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }

    // ===== HISTORY TAB METHODS =====

    /**
     * Refresh history display
     */
    private void refreshHistory() {
        if (historyService == null) {
            return;
        }

        // Create background task to load history
        Task<String> task = new Task<String>() {
            @Override
            protected String call() throws Exception {
                String historyType = historyTypeComboBox.getValue();
                if (historyType == null) {
                    historyType = "All History";
                }

                StringBuilder result = new StringBuilder();
                result.append("=== ").append(historyType).append(" ===\n\n");

                switch (historyType) {
                    case "Generation History":
                        var genHistory = historyService.getGenerationHistory();
                        result.append("Total Generated Cryptographs: ").append(genHistory.size()).append("\n\n");
                        for (var record : genHistory) {
                            result.append("ID: ").append(record.getCryptographId()).append("\n");
                            result.append("Person: ").append(record.getPersonName()).append("\n");
                            result.append("Document: ").append(record.getDocumentType()).append(" - ").append(record.getDocumentNumber()).append("\n");
                            result.append("Method: ").append(record.getGenerationMethod()).append("\n");
                            result.append("Size: ").append(record.getDataSize()).append(" bytes\n");
                            result.append("Created: ").append(record.getCreatedAt()).append("\n");
                            result.append("---\n");
                        }
                        break;

                    case "Scan History":
                        var scanHistory = historyService.getScanHistory();
                        result.append("Total Scans: ").append(scanHistory.size()).append("\n\n");
                        for (var record : scanHistory) {
                            result.append("Cryptograph ID: ").append(record.getCryptographId()).append("\n");
                            result.append("Result: ").append(record.getScanResult()).append("\n");
                            result.append("Confidence: ").append(String.format("%.2f%%", record.getScanConfidence() * 100)).append("\n");
                            result.append("Method: ").append(record.getScanMethod()).append("\n");
                            result.append("Scanned: ").append(record.getScannedAt()).append("\n");
                            result.append("---\n");
                        }
                        break;

                    case "Comparison History":
                        var compHistory = historyService.getComparisonHistory();
                        result.append("Total Comparisons: ").append(compHistory.size()).append("\n\n");
                        for (var record : compHistory) {
                            result.append("Type: ").append(record.getComparisonType()).append("\n");
                            result.append("Source: ").append(record.getSourceId()).append("\n");
                            result.append("Target: ").append(record.getTargetId()).append("\n");
                            result.append("Score: ").append(String.format("%.3f", record.getComparisonScore())).append("\n");
                            result.append("Match: ").append(record.isMatchResult() ? "YES" : "NO").append("\n");
                            result.append("Method: ").append(record.getComparisonMethod()).append("\n");
                            result.append("Compared: ").append(record.getComparedAt()).append("\n");
                            result.append("---\n");
                        }
                        break;

                    case "Audit Log":
                        var auditLog = historyService.getAuditLog();
                        result.append("Recent Audit Entries: ").append(auditLog.size()).append("\n\n");
                        for (var record : auditLog) {
                            result.append("Operation: ").append(record.getOperationType()).append("\n");
                            result.append("Entity: ").append(record.getEntityType()).append(" - ").append(record.getEntityId()).append("\n");
                            result.append("User: ").append(record.getUserInfo()).append("\n");
                            result.append("Success: ").append(record.isSuccess() ? "YES" : "NO").append("\n");
                            result.append("Details: ").append(record.getOperationDetails()).append("\n");
                            if (record.getErrorMessage() != null) {
                                result.append("Error: ").append(record.getErrorMessage()).append("\n");
                            }
                            result.append("Time: ").append(record.getCreatedAt()).append("\n");
                            result.append("---\n");
                        }
                        break;

                    default: // All History
                        var stats = historyService.getSystemStatistics();
                        result.append("System Statistics:\n");
                        result.append("Total Cryptographs: ").append(stats.getIntStat("totalCryptographs")).append("\n");
                        result.append("Total Scans: ").append(stats.getIntStat("totalScans")).append("\n");
                        result.append("Total Comparisons: ").append(stats.getIntStat("totalComparisons")).append("\n");
                        result.append("Successful Matches: ").append(stats.getIntStat("successfulMatches")).append("\n");
                        result.append("Average Scan Confidence: ").append(String.format("%.2f%%", stats.getDoubleStat("avgScanConfidence") * 100)).append("\n");
                        result.append("Average Comparison Score: ").append(String.format("%.3f", stats.getDoubleStat("avgComparisonScore"))).append("\n");
                        break;
                }

                return result.toString();
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    String result = getValue();
                    historyDetailsArea.setText(result);
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Failed to load history", exception);
                    historyDetailsArea.setText("Failed to load history: " + exception.getMessage());
                });
            }
        };

        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }

    // ===== UTILITY METHODS =====

    /**
     * Format comparison result for display
     */
    private String formatComparisonResult(BiometricComparisonService.ComparisonResult result) {
        StringBuilder formatted = new StringBuilder();
        formatted.append("Comparison Result\n\n");
        formatted.append("Match: ").append(result.isMatch() ? "YES" : "NO").append("\n");
        formatted.append("Score: ").append(String.format("%.3f", result.getScore())).append("\n");
        formatted.append("Message: ").append(result.getMessage()).append("\n");
        formatted.append("Time: ").append(result.getComparisonTime()).append("\n");

        if (result instanceof BiometricComparisonService.FingerprintComparisonResult) {
            BiometricComparisonService.FingerprintComparisonResult fpResult =
                (BiometricComparisonService.FingerprintComparisonResult) result;
            formatted.append("Source ID: ").append(fpResult.getSourceId()).append("\n");
            formatted.append("Target ID: ").append(fpResult.getTargetId()).append("\n");
        } else if (result instanceof BiometricComparisonService.FaceComparisonResult) {
            BiometricComparisonService.FaceComparisonResult faceResult =
                (BiometricComparisonService.FaceComparisonResult) result;
            formatted.append("Source ID: ").append(faceResult.getSourceId()).append("\n");
            formatted.append("Target ID: ").append(faceResult.getTargetId()).append("\n");
        } else if (result instanceof BiometricComparisonService.CryptographComparisonResult) {
            BiometricComparisonService.CryptographComparisonResult cryptoResult =
                (BiometricComparisonService.CryptographComparisonResult) result;
            formatted.append("Source ID: ").append(cryptoResult.getSourceId()).append("\n");
            formatted.append("Target ID: ").append(cryptoResult.getTargetId()).append("\n");

            if (cryptoResult.getDetails() != null) {
                formatted.append("\nDetailed Scores:\n");
                formatted.append("Demographic: ").append(String.format("%.3f", cryptoResult.getDetails().getDemographicScore())).append("\n");
                formatted.append("Face: ").append(String.format("%.3f", cryptoResult.getDetails().getFaceScore())).append("\n");
                formatted.append("Fingerprint: ").append(String.format("%.3f", cryptoResult.getDetails().getFingerprintScore())).append("\n");
            }
        }

        return formatted.toString();
    }

    /**
     * Show alert dialog
     */
    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // ===== INNER CLASSES FOR TABLE ITEMS =====

    /**
     * Table item for scan results
     */
    public static class ScanResultTableItem {
        private final String cryptographId;
        private final String result;
        private final String confidence;
        private final String timestamp;

        public ScanResultTableItem(String cryptographId, String result, String confidence, String timestamp) {
            this.cryptographId = cryptographId;
            this.result = result;
            this.confidence = confidence;
            this.timestamp = timestamp;
        }

        public String getCryptographId() { return cryptographId; }
        public String getResult() { return result; }
        public String getConfidence() { return confidence; }
        public String getTimestamp() { return timestamp; }
    }

    /**
     * Table item for history
     */
    public static class HistoryTableItem {
        private final String type;
        private final String description;
        private final String timestamp;
        private final String status;

        public HistoryTableItem(String type, String description, String timestamp, String status) {
            this.type = type;
            this.description = description;
            this.timestamp = timestamp;
            this.status = status;
        }

        public String getType() { return type; }
        public String getDescription() { return description; }
        public String getTimestamp() { return timestamp; }
        public String getStatus() { return status; }
    }
}