import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class CameraService {
  static List<CameraDescription>? _cameras;
  static CameraController? _controller;
  static final ImagePicker _picker = ImagePicker();

  /// Initialize camera service
  static Future<bool> initialize() async {
    try {
      _cameras = await availableCameras();
      return _cameras?.isNotEmpty ?? false;
    } catch (e) {
      debugPrint('Error initializing cameras: $e');
      return false;
    }
  }

  /// Get available cameras
  static List<CameraDescription> get cameras => _cameras ?? [];

  /// Check if cameras are available
  static bool get hasCameras => _cameras?.isNotEmpty ?? false;

  /// Get front camera
  static CameraDescription? get frontCamera {
    return _cameras?.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.front,
      orElse: () => _cameras!.first,
    );
  }

  /// Get back camera
  static CameraDescription? get backCamera {
    return _cameras?.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.back,
      orElse: () => _cameras!.first,
    );
  }

  /// Initialize camera controller
  static Future<CameraController?> initializeController({
    CameraDescription? camera,
    ResolutionPreset resolution = ResolutionPreset.high,
  }) async {
    try {
      if (_cameras == null || _cameras!.isEmpty) {
        await initialize();
      }

      final selectedCamera = camera ?? frontCamera;
      if (selectedCamera == null) return null;

      _controller = CameraController(
        selectedCamera,
        resolution,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller!.initialize();
      return _controller;
    } catch (e) {
      debugPrint('Error initializing camera controller: $e');
      return null;
    }
  }

  /// Dispose camera controller
  static Future<void> disposeController() async {
    await _controller?.dispose();
    _controller = null;
  }

  /// Capture photo with camera
  static Future<File?> capturePhoto({
    String? customPath,
    String prefix = 'photo',
  }) async {
    try {
      if (_controller == null || !_controller!.value.isInitialized) {
        throw Exception('Camera not initialized');
      }

      final XFile photo = await _controller!.takePicture();
      
      // Create custom path if needed
      if (customPath != null) {
        final File photoFile = File(photo.path);
        final File newFile = File(customPath);
        await photoFile.copy(newFile.path);
        await photoFile.delete();
        return newFile;
      }

      return File(photo.path);
    } catch (e) {
      debugPrint('Error capturing photo: $e');
      return null;
    }
  }

  /// Pick image from gallery
  static Future<File?> pickFromGallery({
    ImageSource source = ImageSource.gallery,
    int? imageQuality,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: imageQuality ?? 85,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      debugPrint('Error picking image: $e');
      return null;
    }
  }

  /// Show image source selection dialog
  static Future<File?> showImageSourceDialog(
    BuildContext context, {
    String title = 'Select Image Source',
    bool allowCamera = true,
    bool allowGallery = true,
  }) async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (allowCamera)
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                subtitle: const Text('Take a new photo'),
                onTap: () => Navigator.pop(context, ImageSource.camera),
              ),
            if (allowGallery)
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                subtitle: const Text('Choose from gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (source != null) {
      return await pickFromGallery(source: source);
    }
    return null;
  }

  /// Create custom camera path
  static Future<String> createCustomPath({
    String prefix = 'photo',
    String extension = 'jpg',
  }) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return path.join(appDir.path, '${prefix}_$timestamp.$extension');
  }

  /// Validate image file
  static Future<bool> validateImageFile(File file) async {
    try {
      if (!await file.exists()) return false;
      
      final int fileSize = await file.length();
      if (fileSize == 0) return false;
      
      // Check if it's a valid image by trying to read it
      final Uint8List bytes = await file.readAsBytes();
      if (bytes.isEmpty) return false;
      
      // Basic image format validation
      final String extension = path.extension(file.path).toLowerCase();
      if (!['.jpg', '.jpeg', '.png', '.bmp'].contains(extension)) {
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('Error validating image file: $e');
      return false;
    }
  }

  /// Compress image file
  static Future<File?> compressImage(
    File originalFile, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final String compressedPath = await createCustomPath(
        prefix: 'compressed',
        extension: 'jpg',
      );

      // For now, just copy the file
      // In a real implementation, you would use image compression libraries
      await originalFile.copy(compressedPath);
      return File(compressedPath);
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }

  /// Get image metadata
  static Future<Map<String, dynamic>> getImageMetadata(File imageFile) async {
    try {
      final int fileSize = await imageFile.length();
      final DateTime lastModified = await imageFile.lastModified();
      
      return {
        'path': imageFile.path,
        'size': fileSize,
        'sizeFormatted': _formatFileSize(fileSize),
        'lastModified': lastModified.toIso8601String(),
        'extension': path.extension(imageFile.path),
        'name': path.basename(imageFile.path),
      };
    } catch (e) {
      debugPrint('Error getting image metadata: $e');
      return {};
    }
  }

  /// Format file size
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Clean up temporary files
  static Future<void> cleanupTempFiles() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final List<FileSystemEntity> files = appDir.listSync();
      
      final DateTime cutoff = DateTime.now().subtract(const Duration(hours: 24));
      
      for (final FileSystemEntity file in files) {
        if (file is File) {
          final DateTime lastModified = await file.lastModified();
          if (lastModified.isBefore(cutoff)) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up temp files: $e');
    }
  }

  /// Check camera permissions
  static Future<bool> checkCameraPermission() async {
    try {
      // This would typically use permission_handler package
      // For now, we'll assume permissions are granted
      return true;
    } catch (e) {
      debugPrint('Error checking camera permission: $e');
      return false;
    }
  }

  /// Request camera permissions
  static Future<bool> requestCameraPermission() async {
    try {
      // This would typically use permission_handler package
      // For now, we'll assume permissions are granted
      return true;
    } catch (e) {
      debugPrint('Error requesting camera permission: $e');
      return false;
    }
  }
}

/// Camera capture result
class CameraCaptureResult {
  final File? imageFile;
  final String? error;
  final Map<String, dynamic> metadata;

  CameraCaptureResult({
    this.imageFile,
    this.error,
    this.metadata = const {},
  });

  bool get isSuccess => imageFile != null && error == null;
  bool get hasError => error != null;
}

/// Image source options
enum ImageSourceOption {
  camera,
  gallery,
  both,
}

/// Camera configuration
class CameraConfig {
  final ResolutionPreset resolution;
  final CameraLensDirection lensDirection;
  final bool enableAudio;
  final ImageFormatGroup imageFormat;
  final int imageQuality;

  const CameraConfig({
    this.resolution = ResolutionPreset.high,
    this.lensDirection = CameraLensDirection.back,
    this.enableAudio = false,
    this.imageFormat = ImageFormatGroup.jpeg,
    this.imageQuality = 85,
  });
}
