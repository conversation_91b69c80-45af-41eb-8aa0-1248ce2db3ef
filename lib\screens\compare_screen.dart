import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../models/biometric_models.dart';
import '../providers/biometric_provider.dart';
import 'home_screen.dart';

class CompareScreen extends StatefulWidget {
  const CompareScreen({super.key});

  @override
  State<CompareScreen> createState() => _CompareScreenState();
}

class _CompareScreenState extends State<CompareScreen> {
  File? _fingerprint1;
  File? _fingerprint2;
  String? _selectedCryptographId;
  final ImagePicker _picker = ImagePicker();

  Future<void> _selectFingerprint1() async {
    final source = await _showImageSourceDialog('First Fingerprint');
    if (source != null) {
      final XFile? image = await _picker.pickImage(source: source);
      if (image != null) {
        setState(() {
          _fingerprint1 = File(image.path);
        });
      }
    }
  }

  Future<void> _selectFingerprint2() async {
    final source = await _showImageSourceDialog('Second Fingerprint');
    if (source != null) {
      final XFile? image = await _picker.pickImage(source: source);
      if (image != null) {
        setState(() {
          _fingerprint2 = File(image.path);
        });
      }
    }
  }

  Future<ImageSource?> _showImageSourceDialog(String title) async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select $title Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              subtitle: const Text('Capture fingerprint with camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              subtitle: const Text('Select fingerprint from gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectFromCryptograph() async {
    final provider = context.read<BiometricProvider>();
    await provider.loadCryptographs();
    
    if (provider.cryptographs.isEmpty) {
      _showError('No cryptographs found. Generate a cryptograph first.');
      return;
    }

    final selectedRecord = await showDialog<CryptographRecord>(
      context: context,
      builder: (context) => CryptographSelectionDialog(
        cryptographs: provider.cryptographs,
      ),
    );

    if (selectedRecord != null) {
      setState(() {
        _selectedCryptographId = selectedRecord.cryptographId;
        // In a real implementation, we would extract the fingerprint from the cryptograph
        // For now, we'll use the cryptograph path as the fingerprint path
        _fingerprint2 = File(selectedRecord.cryptographPath);
      });
    }
  }

  Future<void> _compareFingerprints() async {
    if (_fingerprint1 == null) {
      _showError('Please select the first fingerprint');
      return;
    }
    if (_fingerprint2 == null) {
      _showError('Please select the second fingerprint or choose from cryptograph');
      return;
    }

    final provider = context.read<BiometricProvider>();
    final success = await provider.compareFingerprints(
      fingerprint1Path: _fingerprint1!.path,
      fingerprint2Path: _fingerprint2!.path,
    );

    if (success && mounted) {
      _showComparisonResults();
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showComparisonResults() {
    final result = context.read<BiometricProvider>().lastComparisonResult;
    if (result != null) {
      showDialog(
        context: context,
        builder: (context) => ComparisonResultDialog(result: result),
      );
    }
  }

  void _clearSelection() {
    setState(() {
      _fingerprint1 = null;
      _fingerprint2 = null;
      _selectedCryptographId = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Compare Fingerprints'),
        actions: [
          IconButton(
            onPressed: _clearSelection,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear All',
          ),
        ],
      ),
      body: Consumer<BiometricProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Error banner
                    if (provider.hasError)
                      ErrorBanner(
                        message: provider.errorMessage!,
                        onDismiss: provider.clearError,
                      ),

                    // Instructions Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.blue[600],
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Fingerprint Comparison',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              '1. Select the first fingerprint to compare\n'
                              '2. Select the second fingerprint or choose from existing cryptographs\n'
                              '3. Tap "Compare Fingerprints" to get the match score\n'
                              '4. View the detailed comparison results',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Fingerprint Selection Cards
                    Row(
                      children: [
                        Expanded(
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  const Text(
                                    'First Fingerprint',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: _fingerprint1 != null
                                        ? ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: Image.file(
                                              _fingerprint1!,
                                              fit: BoxFit.cover,
                                            ),
                                          )
                                        : const Icon(
                                            Icons.fingerprint,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                  ),
                                  const SizedBox(height: 12),
                                  ElevatedButton.icon(
                                    onPressed: _selectFingerprint1,
                                    icon: const Icon(Icons.add_photo_alternate),
                                    label: const Text('Select'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  const Text(
                                    'Second Fingerprint',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: _fingerprint2 != null
                                        ? ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: Image.file(
                                              _fingerprint2!,
                                              fit: BoxFit.cover,
                                            ),
                                          )
                                        : const Icon(
                                            Icons.fingerprint,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                  ),
                                  const SizedBox(height: 12),
                                  ElevatedButton.icon(
                                    onPressed: _selectFingerprint2,
                                    icon: const Icon(Icons.add_photo_alternate),
                                    label: const Text('Select'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // OR Divider
                    Row(
                      children: [
                        const Expanded(child: Divider()),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            'OR',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Expanded(child: Divider()),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Select from Cryptograph Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Compare with Existing Cryptograph',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (_selectedCryptographId != null) ...[
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.green[50],
                                  border: Border.all(color: Colors.green[300]!),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: Colors.green[600],
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Selected: $_selectedCryptographId',
                                        style: TextStyle(
                                          color: Colors.green[700],
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 12),
                            ],
                            ElevatedButton.icon(
                              onPressed: _selectFromCryptograph,
                              icon: const Icon(Icons.security),
                              label: const Text('Select from Cryptograph'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.purple[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Compare Button
                    ElevatedButton.icon(
                      onPressed: provider.isComparing || 
                                 _fingerprint1 == null || 
                                 _fingerprint2 == null
                          ? null
                          : _compareFingerprints,
                      icon: provider.isComparing
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.compare_arrows),
                      label: Text(
                        provider.isComparing ? 'Comparing...' : 'Compare Fingerprints',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Recent Comparison Results
                    if (provider.lastComparisonResult != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.history,
                                    color: Colors.orange[600],
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Last Comparison Result',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              _buildComparisonSummary(provider.lastComparisonResult!),
                              const SizedBox(height: 12),
                              ElevatedButton.icon(
                                onPressed: () => _showComparisonResults(),
                                icon: const Icon(Icons.visibility),
                                label: const Text('View Full Details'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              // Loading overlay
              if (provider.isComparing)
                const LoadingOverlay(
                  message: 'Comparing fingerprints...\nAnalyzing biometric patterns.',
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildComparisonSummary(ComparisonResult result) {
    final isMatch = result.matchScore >= result.threshold;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isMatch ? Icons.check_circle : Icons.cancel,
              color: isMatch ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 8),
            Text(
              isMatch ? 'MATCH' : 'NO MATCH',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isMatch ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Match Score: ${result.matchScore.toStringAsFixed(2)}%',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'Threshold: ${result.threshold.toStringAsFixed(2)}%',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'Compared: ${result.comparedAt}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}

class CryptographSelectionDialog extends StatelessWidget {
  final List<CryptographRecord> cryptographs;

  const CryptographSelectionDialog({
    super.key,
    required this.cryptographs,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Cryptograph'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: ListView.builder(
          itemCount: cryptographs.length,
          itemBuilder: (context, index) {
            final record = cryptographs[index];
            return ListTile(
              leading: const Icon(Icons.security),
              title: Text(record.cryptographId),
              subtitle: Text(
                'Created: ${record.createdAt}\n'
                'Finger: ${record.fingerType}',
              ),
              isThreeLine: true,
              onTap: () => Navigator.of(context).pop(record),
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}

class ComparisonResultDialog extends StatelessWidget {
  final ComparisonResult result;

  const ComparisonResultDialog({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    final isMatch = result.matchScore >= result.threshold;
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            isMatch ? Icons.check_circle : Icons.cancel,
            color: isMatch ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Text(isMatch ? 'Match Found' : 'No Match'),
        ],
      ),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildResultItem('Match Score', '${result.matchScore.toStringAsFixed(2)}%'),
          _buildResultItem('Threshold', '${result.threshold.toStringAsFixed(2)}%'),
          _buildResultItem('Confidence', '${result.confidence.toStringAsFixed(2)}%'),
          _buildResultItem('Algorithm', result.algorithm),
          _buildResultItem('Compared At', result.comparedAt),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isMatch ? Colors.green[50] : Colors.red[50],
              border: Border.all(
                color: isMatch ? Colors.green[300]! : Colors.red[300]!,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isMatch ? Icons.verified : Icons.error,
                  color: isMatch ? Colors.green[700] : Colors.red[700],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isMatch 
                        ? 'The fingerprints match with high confidence.'
                        : 'The fingerprints do not match.',
                    style: TextStyle(
                      color: isMatch ? Colors.green[700] : Colors.red[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildResultItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
