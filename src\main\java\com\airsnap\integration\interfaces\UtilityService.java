package com.airsnap.integration.interfaces;

import java.util.List;
import java.util.Map;

/**
 * Interface for Utility functionality
 * Represents OmnimatchUtil and TLVDecode libraries
 */
public interface UtilityService {
    
    /**
     * Initialize the utility service
     * @return true if initialization successful
     */
    boolean initialize();
    
    /**
     * Parse TLV (Tag-Length-Value) data
     * @param tlvData Raw TLV data
     * @return Parsed TLV structure
     */
    List<TLVElement> parseTLV(byte[] tlvData);
    
    /**
     * Encode data to TLV format
     * @param elements List of TLV elements
     * @return Encoded TLV data
     */
    byte[] encodeTLV(List<TLVElement> elements);
    
    /**
     * Find TLV element by tag
     * @param tlvData TLV data
     * @param tag Tag to search for
     * @return TLV element or null if not found
     */
    TLVElement findTLVElement(byte[] tlvData, int tag);
    
    /**
     * Perform omnimatch comparison
     * @param template1 First template
     * @param template2 Second template
     * @param matchType Type of matching algorithm
     * @return Match result
     */
    OmniMatchResult performOmniMatch(byte[] template1, byte[] template2, MatchType matchType);
    
    /**
     * Calculate data checksum
     * @param data Input data
     * @param algorithm Checksum algorithm
     * @return Checksum value
     */
    byte[] calculateChecksum(byte[] data, String algorithm);
    
    /**
     * Validate data integrity
     * @param data Data to validate
     * @param expectedChecksum Expected checksum
     * @param algorithm Checksum algorithm
     * @return true if data is valid
     */
    boolean validateDataIntegrity(byte[] data, byte[] expectedChecksum, String algorithm);
    
    /**
     * Convert data format
     * @param data Input data
     * @param fromFormat Source format
     * @param toFormat Target format
     * @return Converted data
     */
    byte[] convertDataFormat(byte[] data, DataFormat fromFormat, DataFormat toFormat);
    
    /**
     * Compress data
     * @param data Data to compress
     * @param algorithm Compression algorithm
     * @return Compressed data
     */
    byte[] compressData(byte[] data, String algorithm);
    
    /**
     * Decompress data
     * @param compressedData Compressed data
     * @param algorithm Compression algorithm
     * @return Decompressed data
     */
    byte[] decompressData(byte[] compressedData, String algorithm);
    
    /**
     * Generate utility report
     * @param operations List of performed operations
     * @return Utility report
     */
    UtilityReport generateReport(List<String> operations);
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Enums and inner classes
    public enum MatchType {
        EXACT,
        FUZZY,
        TEMPLATE,
        BIOMETRIC
    }
    
    public enum DataFormat {
        RAW,
        BASE64,
        HEX,
        JSON,
        XML,
        BINARY
    }
    
    public static class TLVElement {
        private final int tag;
        private final int length;
        private final byte[] value;
        
        public TLVElement(int tag, int length, byte[] value) {
            this.tag = tag;
            this.length = length;
            this.value = value;
        }
        
        // Getters
        public int getTag() { return tag; }
        public int getLength() { return length; }
        public byte[] getValue() { return value; }
        
        @Override
        public String toString() {
            return String.format("TLV[Tag=0x%02X, Length=%d, Value=%s]", 
                tag, length, bytesToHex(value));
        }
        
        private String bytesToHex(byte[] bytes) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02X", b));
            }
            return sb.toString();
        }
    }
    
    public static class OmniMatchResult {
        private final boolean matched;
        private final double score;
        private final MatchType matchType;
        private final Map<String, Object> details;
        
        public OmniMatchResult(boolean matched, double score, MatchType matchType, Map<String, Object> details) {
            this.matched = matched;
            this.score = score;
            this.matchType = matchType;
            this.details = details;
        }
        
        // Getters
        public boolean isMatched() { return matched; }
        public double getScore() { return score; }
        public MatchType getMatchType() { return matchType; }
        public Map<String, Object> getDetails() { return details; }
    }
    
    public static class UtilityReport {
        private final long timestamp;
        private final List<String> operations;
        private final Map<String, Object> statistics;
        private final List<String> errors;
        
        public UtilityReport(long timestamp, List<String> operations, Map<String, Object> statistics, List<String> errors) {
            this.timestamp = timestamp;
            this.operations = operations;
            this.statistics = statistics;
            this.errors = errors;
        }
        
        // Getters
        public long getTimestamp() { return timestamp; }
        public List<String> getOperations() { return operations; }
        public Map<String, Object> getStatistics() { return statistics; }
        public List<String> getErrors() { return errors; }
    }
}
