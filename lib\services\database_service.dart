import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/biometric_models.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'airsnap_biometric.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _cryptographsTable = 'cryptographs';
  static const String _operationHistoryTable = 'operation_history';

  /// Get database instance
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize database
  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// Create database tables
  static Future<void> _onCreate(Database db, int version) async {
    // Create cryptographs table
    await db.execute('''
      CREATE TABLE $_cryptographsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cryptographId TEXT UNIQUE NOT NULL,
        cryptographPath TEXT NOT NULL,
        demographicData TEXT NOT NULL,
        fingerType TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        notes TEXT
      )
    ''');

    // Create operation history table
    await db.execute('''
      CREATE TABLE $_operationHistoryTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operationType TEXT NOT NULL,
        operationData TEXT NOT NULL,
        result TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        success INTEGER NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('''
      CREATE INDEX idx_cryptographs_id ON $_cryptographsTable(cryptographId)
    ''');

    await db.execute('''
      CREATE INDEX idx_cryptographs_created ON $_cryptographsTable(createdAt)
    ''');

    await db.execute('''
      CREATE INDEX idx_history_type ON $_operationHistoryTable(operationType)
    ''');

    await db.execute('''
      CREATE INDEX idx_history_timestamp ON $_operationHistoryTable(timestamp)
    ''');
  }

  /// Handle database upgrades
  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle future database schema upgrades
    if (oldVersion < newVersion) {
      // Add upgrade logic here when needed
    }
  }

  /// Insert cryptograph record
  static Future<int> insertCryptograph(CryptographRecord record) async {
    final db = await database;
    
    try {
      int id = await db.insert(
        _cryptographsTable,
        record.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      // Log operation
      await _logOperation(
        'INSERT_CRYPTOGRAPH',
        jsonEncode({'cryptographId': record.cryptographId}),
        'Cryptograph inserted with ID: $id',
        true,
      );
      
      return id;
    } catch (e) {
      await _logOperation(
        'INSERT_CRYPTOGRAPH',
        jsonEncode({'cryptographId': record.cryptographId}),
        'Error: $e',
        false,
      );
      rethrow;
    }
  }

  /// Get all cryptographs
  static Future<List<CryptographRecord>> getAllCryptographs() async {
    final db = await database;
    
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        _cryptographsTable,
        orderBy: 'createdAt DESC',
      );
      
      return List.generate(maps.length, (i) {
        return CryptographRecord.fromMap(maps[i]);
      });
    } catch (e) {
      await _logOperation(
        'GET_ALL_CRYPTOGRAPHS',
        '{}',
        'Error: $e',
        false,
      );
      return [];
    }
  }

  /// Get cryptograph by ID
  static Future<CryptographRecord?> getCryptographById(String cryptographId) async {
    final db = await database;
    
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        _cryptographsTable,
        where: 'cryptographId = ?',
        whereArgs: [cryptographId],
      );
      
      if (maps.isNotEmpty) {
        return CryptographRecord.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      await _logOperation(
        'GET_CRYPTOGRAPH_BY_ID',
        jsonEncode({'cryptographId': cryptographId}),
        'Error: $e',
        false,
      );
      return null;
    }
  }

  /// Update cryptograph record
  static Future<int> updateCryptograph(CryptographRecord record) async {
    final db = await database;
    
    try {
      int count = await db.update(
        _cryptographsTable,
        record.toMap(),
        where: 'id = ?',
        whereArgs: [record.id],
      );
      
      await _logOperation(
        'UPDATE_CRYPTOGRAPH',
        jsonEncode({'id': record.id, 'cryptographId': record.cryptographId}),
        'Cryptograph updated, rows affected: $count',
        count > 0,
      );
      
      return count;
    } catch (e) {
      await _logOperation(
        'UPDATE_CRYPTOGRAPH',
        jsonEncode({'id': record.id}),
        'Error: $e',
        false,
      );
      rethrow;
    }
  }

  /// Delete cryptograph record
  static Future<int> deleteCryptograph(int id) async {
    final db = await database;
    
    try {
      int count = await db.delete(
        _cryptographsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      await _logOperation(
        'DELETE_CRYPTOGRAPH',
        jsonEncode({'id': id}),
        'Cryptograph deleted, rows affected: $count',
        count > 0,
      );
      
      return count;
    } catch (e) {
      await _logOperation(
        'DELETE_CRYPTOGRAPH',
        jsonEncode({'id': id}),
        'Error: $e',
        false,
      );
      rethrow;
    }
  }

  /// Search cryptographs by name
  static Future<List<CryptographRecord>> searchCryptographsByName(String name) async {
    final db = await database;
    
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        _cryptographsTable,
        where: 'demographicData LIKE ?',
        whereArgs: ['%$name%'],
        orderBy: 'createdAt DESC',
      );
      
      return List.generate(maps.length, (i) {
        return CryptographRecord.fromMap(maps[i]);
      });
    } catch (e) {
      await _logOperation(
        'SEARCH_CRYPTOGRAPHS',
        jsonEncode({'searchTerm': name}),
        'Error: $e',
        false,
      );
      return [];
    }
  }

  /// Get cryptographs by date range
  static Future<List<CryptographRecord>> getCryptographsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        _cryptographsTable,
        where: 'createdAt BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'createdAt DESC',
      );
      
      return List.generate(maps.length, (i) {
        return CryptographRecord.fromMap(maps[i]);
      });
    } catch (e) {
      await _logOperation(
        'GET_CRYPTOGRAPHS_BY_DATE',
        jsonEncode({
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        }),
        'Error: $e',
        false,
      );
      return [];
    }
  }

  /// Log operation to history
  static Future<void> _logOperation(
    String operationType,
    String operationData,
    String result,
    bool success,
  ) async {
    try {
      final db = await database;
      
      final operation = OperationHistory(
        operationType: operationType,
        operationData: operationData,
        result: result,
        timestamp: DateTime.now().toIso8601String(),
        success: success,
      );
      
      await db.insert(
        _operationHistoryTable,
        operation.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      // Silent fail for logging to prevent infinite loops
      print('Failed to log operation: $e');
    }
  }

  /// Get operation history
  static Future<List<OperationHistory>> getOperationHistory({
    int limit = 100,
    String? operationType,
  }) async {
    final db = await database;
    
    try {
      String whereClause = '';
      List<dynamic> whereArgs = [];
      
      if (operationType != null) {
        whereClause = 'operationType = ?';
        whereArgs.add(operationType);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        _operationHistoryTable,
        where: whereClause.isEmpty ? null : whereClause,
        whereArgs: whereArgs.isEmpty ? null : whereArgs,
        orderBy: 'timestamp DESC',
        limit: limit,
      );
      
      return List.generate(maps.length, (i) {
        return OperationHistory.fromMap(maps[i]);
      });
    } catch (e) {
      print('Failed to get operation history: $e');
      return [];
    }
  }

  /// Clear old operation history (keep last 1000 records)
  static Future<void> cleanupOperationHistory() async {
    final db = await database;
    
    try {
      await db.execute('''
        DELETE FROM $_operationHistoryTable 
        WHERE id NOT IN (
          SELECT id FROM $_operationHistoryTable 
          ORDER BY timestamp DESC 
          LIMIT 1000
        )
      ''');
    } catch (e) {
      print('Failed to cleanup operation history: $e');
    }
  }

  /// Get database statistics
  static Future<Map<String, int>> getDatabaseStats() async {
    final db = await database;
    
    try {
      final cryptographCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM $_cryptographsTable'),
      ) ?? 0;
      
      final historyCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM $_operationHistoryTable'),
      ) ?? 0;
      
      return {
        'cryptographs': cryptographCount,
        'operations': historyCount,
      };
    } catch (e) {
      return {
        'cryptographs': 0,
        'operations': 0,
      };
    }
  }

  /// Close database connection
  static Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
