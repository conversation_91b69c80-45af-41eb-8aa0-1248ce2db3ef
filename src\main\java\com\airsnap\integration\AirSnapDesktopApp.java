package com.airsnap.integration;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.embed.swing.SwingFXUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.airsnap.integration.impl.ComputerVisionServiceImpl;
import com.airsnap.integration.interfaces.ComputerVisionService;
import com.airsnap.integration.interfaces.ComputerVisionService.*;

/**
 * Main JavaFX Application for AirSnap Computer Vision Testing
 */
public class AirSnapDesktopApp extends Application {
    
    private ComputerVisionService cvService;
    private ImageView originalImageView;
    private ImageView processedImageView;
    private TextArea resultsArea;
    private BufferedImage currentImage;
    private Label statusLabel;
    
    @Override
    public void start(Stage primaryStage) {
        // Initialize services
        cvService = new ComputerVisionServiceImpl();
        
        // Create UI
        primaryStage.setTitle("AirSnap Computer Vision Test Application");
        
        // Main layout
        BorderPane root = new BorderPane();
        root.setPadding(new Insets(10));
        
        // Top menu bar
        MenuBar menuBar = createMenuBar(primaryStage);
        root.setTop(menuBar);
        
        // Center content
        VBox centerContent = createCenterContent();
        root.setCenter(centerContent);
        
        // Bottom status bar
        statusLabel = new Label("Ready - Click 'Initialize Service' to begin");
        statusLabel.setStyle("-fx-background-color: #f0f0f0; -fx-padding: 5px;");
        root.setBottom(statusLabel);
        
        // Create scene
        Scene scene = new Scene(root, 1200, 800);
        try {
            scene.getStylesheets().add(getClass().getResource("/styles.css").toExternalForm());
        } catch (Exception e) {
            // CSS file not found, continue without styling
            System.out.println("Warning: Could not load CSS file");
        }
        
        primaryStage.setScene(scene);
        primaryStage.show();
    }
    
    private MenuBar createMenuBar(Stage stage) {
        MenuBar menuBar = new MenuBar();
        
        // File menu
        Menu fileMenu = new Menu("File");
        MenuItem loadImage = new MenuItem("Load Image");
        loadImage.setOnAction(e -> loadImageFile(stage));
        MenuItem exit = new MenuItem("Exit");
        exit.setOnAction(e -> System.exit(0));
        fileMenu.getItems().addAll(loadImage, new SeparatorMenuItem(), exit);
        
        // Service menu
        Menu serviceMenu = new Menu("Service");
        MenuItem initService = new MenuItem("Initialize Service");
        initService.setOnAction(e -> initializeService());
        MenuItem cleanupService = new MenuItem("Cleanup Service");
        cleanupService.setOnAction(e -> cleanupService());
        serviceMenu.getItems().addAll(initService, cleanupService);
        
        menuBar.getMenus().addAll(fileMenu, serviceMenu);
        return menuBar;
    }
    
    private VBox createCenterContent() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(10));
        
        // Image display section
        HBox imageSection = createImageSection();
        
        // Control panel
        VBox controlPanel = createControlPanel();
        
        // Results area
        resultsArea = new TextArea();
        resultsArea.setPrefRowCount(8);
        resultsArea.setEditable(false);
        resultsArea.setWrapText(true);
        
        content.getChildren().addAll(imageSection, controlPanel, 
                                   new Label("Results:"), resultsArea);
        
        return content;
    }
    
    private HBox createImageSection() {
        HBox imageSection = new HBox(20);
        imageSection.setAlignment(Pos.CENTER);
        
        // Original image
        VBox originalBox = new VBox(5);
        originalBox.setAlignment(Pos.CENTER);
        Label originalLabel = new Label("Original Image");
        originalLabel.setStyle("-fx-font-weight: bold;");
        originalImageView = new ImageView();
        originalImageView.setFitWidth(300);
        originalImageView.setFitHeight(300);
        originalImageView.setPreserveRatio(true);
        originalImageView.setStyle("-fx-border-color: gray; -fx-border-width: 1;");
        originalBox.getChildren().addAll(originalLabel, originalImageView);
        
        // Processed image
        VBox processedBox = new VBox(5);
        processedBox.setAlignment(Pos.CENTER);
        Label processedLabel = new Label("Processed Image");
        processedLabel.setStyle("-fx-font-weight: bold;");
        processedImageView = new ImageView();
        processedImageView.setFitWidth(300);
        processedImageView.setFitHeight(300);
        processedImageView.setPreserveRatio(true);
        processedImageView.setStyle("-fx-border-color: gray; -fx-border-width: 1;");
        processedBox.getChildren().addAll(processedLabel, processedImageView);
        
        imageSection.getChildren().addAll(originalBox, processedBox);
        return imageSection;
    }
    
    private VBox createControlPanel() {
        VBox controlPanel = new VBox(10);
        controlPanel.setPadding(new Insets(10));
        controlPanel.setStyle("-fx-border-color: lightgray; -fx-border-width: 1; -fx-border-radius: 5;");
        
        Label controlLabel = new Label("Computer Vision Operations");
        controlLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");
        
        // Create button grid
        GridPane buttonGrid = new GridPane();
        buttonGrid.setHgap(10);
        buttonGrid.setVgap(10);
        buttonGrid.setAlignment(Pos.CENTER);
        
        // Row 1: Object Detection and Feature Extraction
        Button detectObjectsBtn = new Button("Detect Objects");
        detectObjectsBtn.setPrefWidth(150);
        detectObjectsBtn.setOnAction(e -> detectObjects());
        
        Button extractFeaturesBtn = new Button("Extract Features");
        extractFeaturesBtn.setPrefWidth(150);
        extractFeaturesBtn.setOnAction(e -> extractFeatures());
        
        // Row 2: Image Enhancement and Segmentation
        Button enhanceImageBtn = new Button("Enhance Image");
        enhanceImageBtn.setPrefWidth(150);
        enhanceImageBtn.setOnAction(e -> enhanceImage());
        
        Button segmentImageBtn = new Button("Segment Image");
        segmentImageBtn.setPrefWidth(150);
        segmentImageBtn.setOnAction(e -> segmentImage());
        
        // Row 3: OCR and Neural Network
        Button performOCRBtn = new Button("Perform OCR");
        performOCRBtn.setPrefWidth(150);
        performOCRBtn.setOnAction(e -> performOCR());
        
        Button neuralNetworkBtn = new Button("Neural Network");
        neuralNetworkBtn.setPrefWidth(150);
        neuralNetworkBtn.setOnAction(e -> processWithNeuralNetwork());
        
        // Row 4: Apply Filters and Statistics
        Button applyFiltersBtn = new Button("Apply Filters");
        applyFiltersBtn.setPrefWidth(150);
        applyFiltersBtn.setOnAction(e -> applyFilters());
        
        Button showStatsBtn = new Button("Show Statistics");
        showStatsBtn.setPrefWidth(150);
        showStatsBtn.setOnAction(e -> showStatistics());
        
        // Add buttons to grid
        buttonGrid.add(detectObjectsBtn, 0, 0);
        buttonGrid.add(extractFeaturesBtn, 1, 0);
        buttonGrid.add(enhanceImageBtn, 0, 1);
        buttonGrid.add(segmentImageBtn, 1, 1);
        buttonGrid.add(performOCRBtn, 0, 2);
        buttonGrid.add(neuralNetworkBtn, 1, 2);
        buttonGrid.add(applyFiltersBtn, 0, 3);
        buttonGrid.add(showStatsBtn, 1, 3);
        
        controlPanel.getChildren().addAll(controlLabel, buttonGrid);
        return controlPanel;
    }
    
    private void loadImageFile(Stage stage) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Image File");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp")
        );
        
        File selectedFile = fileChooser.showOpenDialog(stage);
        if (selectedFile != null) {
            try {
                currentImage = ImageIO.read(selectedFile);
                Image fxImage = SwingFXUtils.toFXImage(currentImage, null);
                originalImageView.setImage(fxImage);
                updateStatus("Image loaded: " + selectedFile.getName());
                resultsArea.appendText("Loaded image: " + selectedFile.getName() + 
                                     " (" + currentImage.getWidth() + "x" + currentImage.getHeight() + ")\n");
            } catch (IOException e) {
                showError("Failed to load image: " + e.getMessage());
            }
        }
    }
    
    private void initializeService() {
        updateStatus("Initializing Computer Vision Service...");
        new Thread(() -> {
            boolean success = cvService.initialize();
            javafx.application.Platform.runLater(() -> {
                if (success) {
                    updateStatus("Computer Vision Service initialized successfully");
                    resultsArea.appendText("✓ Computer Vision Service initialized\n");
                } else {
                    updateStatus("Failed to initialize Computer Vision Service");
                    resultsArea.appendText("✗ Failed to initialize Computer Vision Service\n");
                }
            });
        }).start();
    }
    
    private void cleanupService() {
        cvService.cleanup();
        updateStatus("Service cleaned up");
        resultsArea.appendText("Service resources cleaned up\n");
    }
    
    private void detectObjects() {
        if (!checkImageAndService()) return;
        
        updateStatus("Detecting objects...");
        new Thread(() -> {
            try {
                List<ObjectDetectionResult> objects = cvService.detectObjects(currentImage);
                javafx.application.Platform.runLater(() -> {
                    StringBuilder result = new StringBuilder("Object Detection Results:\n");
                    for (ObjectDetectionResult obj : objects) {
                        result.append(String.format("- %s (%.2f%%) at [%d,%d] %dx%d\n", 
                                    obj.getClassName(), obj.getConfidence() * 100,
                                    obj.getX(), obj.getY(), obj.getWidth(), obj.getHeight()));
                    }
                    resultsArea.appendText(result.toString() + "\n");
                    updateStatus("Object detection completed - found " + objects.size() + " objects");
                });
            } catch (Exception e) {
                javafx.application.Platform.runLater(() -> showError("Object detection failed: " + e.getMessage()));
            }
        }).start();
    }
    
    private void extractFeatures() {
        if (!checkImageAndService()) return;
        
        // Show feature type selection dialog
        ChoiceDialog<FeatureType> dialog = new ChoiceDialog<>(FeatureType.SIFT, FeatureType.values());
        dialog.setTitle("Feature Extraction");
        dialog.setHeaderText("Select Feature Type");
        dialog.setContentText("Choose the type of features to extract:");
        
        dialog.showAndWait().ifPresent(featureType -> {
            updateStatus("Extracting " + featureType + " features...");
            new Thread(() -> {
                try {
                    float[] features = cvService.extractImageFeatures(currentImage, featureType);
                    javafx.application.Platform.runLater(() -> {
                        StringBuilder result = new StringBuilder("Feature Extraction Results:\n");
                        result.append("Feature Type: ").append(featureType).append("\n");
                        result.append("Feature Vector Size: ").append(features.length).append("\n");
                        result.append("Sample values: ");
                        for (int i = 0; i < Math.min(10, features.length); i++) {
                            result.append(String.format("%.3f ", features[i]));
                        }
                        if (features.length > 10) result.append("...");
                        result.append("\n\n");
                        resultsArea.appendText(result.toString());
                        updateStatus("Feature extraction completed - " + features.length + " features extracted");
                    });
                } catch (Exception e) {
                    javafx.application.Platform.runLater(() -> showError("Feature extraction failed: " + e.getMessage()));
                }
            }).start();
        });
    }
    
    private boolean checkImageAndService() {
        if (currentImage == null) {
            showError("Please load an image first");
            return false;
        }
        // Note: We'll assume service is initialized for demo purposes
        return true;
    }
    
    private void updateStatus(String message) {
        statusLabel.setText(message);
    }
    
    private void enhanceImage() {
        if (!checkImageAndService()) return;

        // Show enhancement type selection dialog
        ChoiceDialog<EnhancementType> dialog = new ChoiceDialog<>(EnhancementType.BRIGHTNESS_ADJUSTMENT, EnhancementType.values());
        dialog.setTitle("Image Enhancement");
        dialog.setHeaderText("Select Enhancement Type");
        dialog.setContentText("Choose the type of enhancement:");

        dialog.showAndWait().ifPresent(enhancementType -> {
            updateStatus("Enhancing image with " + enhancementType + "...");
            new Thread(() -> {
                try {
                    BufferedImage enhanced = cvService.enhanceImageQuality(currentImage, enhancementType);
                    javafx.application.Platform.runLater(() -> {
                        Image fxImage = SwingFXUtils.toFXImage(enhanced, null);
                        processedImageView.setImage(fxImage);
                        resultsArea.appendText("Image Enhancement Results:\n");
                        resultsArea.appendText("Enhancement Type: " + enhancementType + "\n");
                        resultsArea.appendText("Enhanced image displayed in processed view\n\n");
                        updateStatus("Image enhancement completed");
                    });
                } catch (Exception e) {
                    javafx.application.Platform.runLater(() -> showError("Image enhancement failed: " + e.getMessage()));
                }
            }).start();
        });
    }

    private void segmentImage() {
        if (!checkImageAndService()) return;

        // Show segmentation type selection dialog
        ChoiceDialog<SegmentationType> dialog = new ChoiceDialog<>(SegmentationType.SEMANTIC, SegmentationType.values());
        dialog.setTitle("Image Segmentation");
        dialog.setHeaderText("Select Segmentation Type");
        dialog.setContentText("Choose the type of segmentation:");

        dialog.showAndWait().ifPresent(segmentationType -> {
            updateStatus("Performing " + segmentationType + " segmentation...");
            new Thread(() -> {
                try {
                    BufferedImage segmented = cvService.performImageSegmentation(currentImage, segmentationType);
                    javafx.application.Platform.runLater(() -> {
                        Image fxImage = SwingFXUtils.toFXImage(segmented, null);
                        processedImageView.setImage(fxImage);
                        resultsArea.appendText("Image Segmentation Results:\n");
                        resultsArea.appendText("Segmentation Type: " + segmentationType + "\n");
                        resultsArea.appendText("Segmented image displayed in processed view\n\n");
                        updateStatus("Image segmentation completed");
                    });
                } catch (Exception e) {
                    javafx.application.Platform.runLater(() -> showError("Image segmentation failed: " + e.getMessage()));
                }
            }).start();
        });
    }

    private void performOCR() {
        if (!checkImageAndService()) return;

        updateStatus("Performing OCR...");
        new Thread(() -> {
            try {
                String text = cvService.performOCR(currentImage);
                javafx.application.Platform.runLater(() -> {
                    resultsArea.appendText("OCR Results:\n");
                    resultsArea.appendText("Detected Text: \"" + text + "\"\n\n");
                    updateStatus("OCR completed");
                });
            } catch (Exception e) {
                javafx.application.Platform.runLater(() -> showError("OCR failed: " + e.getMessage()));
            }
        }).start();
    }

    private void processWithNeuralNetwork() {
        if (!checkImageAndService()) return;

        // First load a mock model
        updateStatus("Loading neural network model...");
        new Thread(() -> {
            try {
                boolean modelLoaded = cvService.loadNeuralNetworkModel("/mock/model.onnx", ModelType.CLASSIFICATION);
                if (modelLoaded) {
                    NeuralNetworkResult result = cvService.processWithNeuralNetwork(currentImage, "model.onnx");
                    javafx.application.Platform.runLater(() -> {
                        resultsArea.appendText("Neural Network Processing Results:\n");
                        resultsArea.appendText("Model: Classification Model\n");
                        resultsArea.appendText("Processing Time: " + result.getProcessingTime() + "ms\n");
                        resultsArea.appendText("Predictions:\n");
                        for (Map.Entry<String, Float> entry : result.getPredictions().entrySet()) {
                            resultsArea.appendText("  " + entry.getKey() + ": " +
                                                 String.format("%.2f%%", entry.getValue() * 100) + "\n");
                        }
                        resultsArea.appendText("\n");
                        updateStatus("Neural network processing completed");
                    });
                }
            } catch (Exception e) {
                javafx.application.Platform.runLater(() -> showError("Neural network processing failed: " + e.getMessage()));
            }
        }).start();
    }

    private void applyFilters() {
        if (!checkImageAndService()) return;

        // Create some sample filters
        List<ImageFilter> filters = new ArrayList<>();
        Map<String, Object> blurParams = new HashMap<>();
        blurParams.put("radius", 3);
        filters.add(new ImageFilter("Gaussian Blur", blurParams));

        Map<String, Object> sharpenParams = new HashMap<>();
        sharpenParams.put("strength", 1.5);
        filters.add(new ImageFilter("Sharpen", sharpenParams));

        updateStatus("Applying image filters...");
        new Thread(() -> {
            try {
                BufferedImage filtered = cvService.applyImageFilters(currentImage, filters);
                javafx.application.Platform.runLater(() -> {
                    Image fxImage = SwingFXUtils.toFXImage(filtered, null);
                    processedImageView.setImage(fxImage);
                    resultsArea.appendText("Image Filters Applied:\n");
                    for (ImageFilter filter : filters) {
                        resultsArea.appendText("- " + filter.getName() + "\n");
                    }
                    resultsArea.appendText("Filtered image displayed in processed view\n\n");
                    updateStatus("Image filters applied");
                });
            } catch (Exception e) {
                javafx.application.Platform.runLater(() -> showError("Filter application failed: " + e.getMessage()));
            }
        }).start();
    }

    private void showStatistics() {
        updateStatus("Retrieving processing statistics...");
        try {
            ProcessingStatistics stats = cvService.getProcessingStatistics();
            resultsArea.appendText("Processing Statistics:\n");
            resultsArea.appendText("Total Processed Images: " + stats.getTotalProcessedImages() + "\n");
            resultsArea.appendText("Average Processing Time: " + String.format("%.2f ms", stats.getAverageProcessingTime()) + "\n");
            resultsArea.appendText("Operation Counts:\n");
            for (Map.Entry<String, Long> entry : stats.getOperationCounts().entrySet()) {
                resultsArea.appendText("  " + entry.getKey() + ": " + entry.getValue() + "\n");
            }
            resultsArea.appendText("\n");
            updateStatus("Statistics displayed");
        } catch (Exception e) {
            showError("Failed to retrieve statistics: " + e.getMessage());
        }
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
        updateStatus("Error: " + message);
    }

    public static void main(String[] args) {
        launch(args);
    }
}
