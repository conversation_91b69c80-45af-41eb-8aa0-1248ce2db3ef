@echo off
echo === AirSnap Computer Vision Test Suite ===
echo.

REM Create directories
if not exist "target\classes" mkdir target\classes
if not exist "target\test-classes" mkdir target\test-classes

echo 1. Compiling Java sources...

REM Compile main sources (excluding JavaFX app)
javac -d target\classes -cp "src\main\java" src\main\java\com\airsnap\integration\interfaces\*.java src\main\java\com\airsnap\integration\impl\*.java src\main\java\com\airsnap\integration\TestRunner.java

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to compile main sources
    pause
    exit /b 1
)

echo    ✓ Main sources compiled successfully

REM Compile test sources
javac -d target\test-classes -cp "target\classes;src\test\java" src\test\java\com\airsnap\integration\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to compile test sources
    pause
    exit /b 1
)

echo    ✓ Test sources compiled successfully
echo.

echo 2. Running test suite...
java -cp target\classes com.airsnap.integration.TestRunner

echo.
echo 3. Test suite completed!
echo.
echo To run the JavaFX UI application, you need to:
echo 1. Install JavaFX SDK
echo 2. Add JavaFX modules to the classpath
echo 3. Run: java --module-path /path/to/javafx/lib --add-modules javafx.controls,javafx.fxml,javafx.swing -cp target\classes com.airsnap.integration.AirSnapDesktopApp
echo.
pause
