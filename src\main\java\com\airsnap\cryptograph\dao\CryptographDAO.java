package com.airsnap.cryptograph.dao;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.Cryptograph;
import com.airsnap.cryptograph.model.DemographicInfo;
import com.airsnap.cryptograph.model.BiometricData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for Cryptograph operations
 */
public class CryptographDAO {
    private static final Logger logger = LoggerFactory.getLogger(CryptographDAO.class);
    private final DatabaseManager dbManager;
    
    public CryptographDAO(DatabaseManager dbManager) {
        this.dbManager = dbManager;
    }
    
    /**
     * Save a new cryptograph to the database
     */
    public Long saveCryptograph(Cryptograph cryptograph) throws SQLException {
        String sql = """
            INSERT INTO cryptographs (cryptograph_id, demographic_id, biometric_id, 
                                    cryptograph_data, cryptograph_hash, generation_method)
            VALUES (?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            stmt.setString(1, cryptograph.getCryptographId());
            stmt.setLong(2, cryptograph.getDemographicId());
            stmt.setLong(3, cryptograph.getBiometricId());
            stmt.setBytes(4, cryptograph.getCryptographData());
            stmt.setString(5, cryptograph.getCryptographHash());
            stmt.setString(6, cryptograph.getGenerationMethod());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating cryptograph failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    Long id = generatedKeys.getLong(1);
                    cryptograph.setId(id);
                    
                    // Log audit
                    dbManager.logAudit("CREATE", "CRYPTOGRAPH", cryptograph.getCryptographId(), 
                                     "SYSTEM", "Cryptograph generated", true, null);
                    
                    logger.info("Cryptograph saved with ID: {}", id);
                    return id;
                } else {
                    throw new SQLException("Creating cryptograph failed, no ID obtained.");
                }
            }
        }
    }
    
    /**
     * Find cryptograph by ID
     */
    public Cryptograph findById(Long id) throws SQLException {
        String sql = """
            SELECT c.*, d.first_name, d.last_name, d.date_of_birth, d.gender, d.nationality,
                   d.document_number, d.document_type, d.created_at as demo_created_at
            FROM cryptographs c
            JOIN demographic_info d ON c.demographic_id = d.id
            WHERE c.id = ?
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToCryptograph(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Find cryptograph by cryptograph ID
     */
    public Cryptograph findByCryptographId(String cryptographId) throws SQLException {
        String sql = """
            SELECT c.*, d.first_name, d.last_name, d.date_of_birth, d.gender, d.nationality,
                   d.document_number, d.document_type, d.created_at as demo_created_at
            FROM cryptographs c
            JOIN demographic_info d ON c.demographic_id = d.id
            WHERE c.cryptograph_id = ?
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, cryptographId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToCryptograph(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Get all cryptographs
     */
    public List<Cryptograph> findAll() throws SQLException {
        String sql = """
            SELECT c.*, d.first_name, d.last_name, d.date_of_birth, d.gender, d.nationality,
                   d.document_number, d.document_type, d.created_at as demo_created_at
            FROM cryptographs c
            JOIN demographic_info d ON c.demographic_id = d.id
            ORDER BY c.created_at DESC
        """;
        
        List<Cryptograph> cryptographs = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                cryptographs.add(mapResultSetToCryptograph(rs));
            }
        }
        return cryptographs;
    }
    
    /**
     * Get cryptographs created within a date range
     */
    public List<Cryptograph> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = """
            SELECT c.*, d.first_name, d.last_name, d.date_of_birth, d.gender, d.nationality,
                   d.document_number, d.document_type, d.created_at as demo_created_at
            FROM cryptographs c
            JOIN demographic_info d ON c.demographic_id = d.id
            WHERE c.created_at BETWEEN ? AND ?
            ORDER BY c.created_at DESC
        """;
        
        List<Cryptograph> cryptographs = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setTimestamp(1, Timestamp.valueOf(startDate));
            stmt.setTimestamp(2, Timestamp.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    cryptographs.add(mapResultSetToCryptograph(rs));
                }
            }
        }
        return cryptographs;
    }
    
    /**
     * Delete cryptograph by ID
     */
    public boolean deleteCryptograph(Long id) throws SQLException {
        Cryptograph cryptograph = findById(id);
        if (cryptograph == null) {
            return false;
        }
        
        String sql = "DELETE FROM cryptographs WHERE id = ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            int affectedRows = stmt.executeUpdate();
            
            if (affectedRows > 0) {
                // Log audit
                dbManager.logAudit("DELETE", "CRYPTOGRAPH", cryptograph.getCryptographId(), 
                                 "SYSTEM", "Cryptograph deleted", true, null);
                logger.info("Cryptograph deleted with ID: {}", id);
                return true;
            }
        }
        return false;
    }
    
    /**
     * Map ResultSet to Cryptograph object
     */
    private Cryptograph mapResultSetToCryptograph(ResultSet rs) throws SQLException {
        Cryptograph cryptograph = new Cryptograph();
        cryptograph.setId(rs.getLong("id"));
        cryptograph.setCryptographId(rs.getString("cryptograph_id"));
        cryptograph.setDemographicId(rs.getLong("demographic_id"));
        cryptograph.setBiometricId(rs.getLong("biometric_id"));
        cryptograph.setCryptographData(rs.getBytes("cryptograph_data"));
        cryptograph.setCryptographHash(rs.getString("cryptograph_hash"));
        cryptograph.setGenerationMethod(rs.getString("generation_method"));
        cryptograph.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        
        // Map demographic info
        DemographicInfo demographicInfo = new DemographicInfo();
        demographicInfo.setId(rs.getLong("demographic_id"));
        demographicInfo.setFirstName(rs.getString("first_name"));
        demographicInfo.setLastName(rs.getString("last_name"));
        demographicInfo.setDateOfBirth(rs.getDate("date_of_birth") != null ? 
                                     rs.getDate("date_of_birth").toLocalDate() : null);
        demographicInfo.setGender(rs.getString("gender"));
        demographicInfo.setNationality(rs.getString("nationality"));
        demographicInfo.setDocumentNumber(rs.getString("document_number"));
        demographicInfo.setDocumentType(rs.getString("document_type"));
        
        cryptograph.setDemographicInfo(demographicInfo);
        
        return cryptograph;
    }
}
