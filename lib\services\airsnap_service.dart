import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import '../models/biometric_models.dart';

class AirSnapService {
  static const MethodChannel _channel = MethodChannel('com.airsnap.biometric/native');
  
  static bool _isInitialized = false;
  
  /// Initialize AirSnap libraries
  static Future<InitializationResult> initializeAirSnap() async {
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('initializeAirSnap')
      );
      
      _isInitialized = result['success'] ?? false;
      
      return InitializationResult(
        success: result['success'] ?? false,
        message: result['message'] ?? '',
        version: result['version'] ?? '',
        libraries: List<String>.from(result['libraries'] ?? []),
      );
    } on PlatformException catch (e) {
      return InitializationResult(
        success: false,
        message: 'Platform error: ${e.message}',
        version: '',
        libraries: [],
      );
    } catch (e) {
      return InitializationResult(
        success: false,
        message: 'Unexpected error: $e',
        version: '',
        libraries: [],
      );
    }
  }
  
  /// Generate cryptograph from demographic data and biometric images
  static Future<CryptographResult> generateCryptograph({
    required DemographicData demographicData,
    required String faceImagePath,
    required String fingerprintImagePath,
    required String fingerType,
  }) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('generateCryptograph', {
          'demographicData': jsonEncode(demographicData.toJson()),
          'faceImagePath': faceImagePath,
          'fingerprintImagePath': fingerprintImagePath,
          'fingerType': fingerType,
        })
      );
      
      if (result['success'] == true) {
        return CryptographResult(
          success: true,
          cryptographId: result['cryptographId'] ?? '',
          cryptographPath: result['cryptographPath'] ?? '',
          faceTemplateSize: result['faceTemplateSize'] ?? 0,
          fingerprintTemplateSize: result['fingerprintTemplateSize'] ?? 0,
          fingerType: result['fingerType'] ?? '',
          generatedAt: result['generatedAt'] ?? '',
          errorMessage: null,
        );
      } else {
        return CryptographResult(
          success: false,
          cryptographId: '',
          cryptographPath: '',
          faceTemplateSize: 0,
          fingerprintTemplateSize: 0,
          fingerType: '',
          generatedAt: '',
          errorMessage: result['error'] ?? 'Unknown error',
        );
      }
    } on PlatformException catch (e) {
      return CryptographResult(
        success: false,
        cryptographId: '',
        cryptographPath: '',
        faceTemplateSize: 0,
        fingerprintTemplateSize: 0,
        fingerType: '',
        generatedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Scan cryptograph and extract information
  static Future<ScanResult> scanCryptograph(String cryptographPath) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('scanCryptograph', {
          'cryptographPath': cryptographPath,
        })
      );
      
      if (result['success'] == true) {
        final scannedData = Map<String, dynamic>.from(result['scannedData']);
        
        return ScanResult(
          success: true,
          demographicData: DemographicData.fromJson(scannedData),
          cryptographId: scannedData['cryptographId'] ?? '',
          fingerType: scannedData['fingerType'] ?? '',
          faceTemplatePresent: scannedData['faceTemplatePresent'] ?? false,
          fingerprintTemplatePresent: scannedData['fingerprintTemplatePresent'] ?? false,
          scannedAt: result['scannedAt'] ?? '',
          errorMessage: null,
        );
      } else {
        return ScanResult(
          success: false,
          demographicData: DemographicData.empty(),
          cryptographId: '',
          fingerType: '',
          faceTemplatePresent: false,
          fingerprintTemplatePresent: false,
          scannedAt: '',
          errorMessage: result['error'] ?? 'Unknown error',
        );
      }
    } on PlatformException catch (e) {
      return ScanResult(
        success: false,
        demographicData: DemographicData.empty(),
        cryptographId: '',
        fingerType: '',
        faceTemplatePresent: false,
        fingerprintTemplatePresent: false,
        scannedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Compare two fingerprints
  static Future<ComparisonResult> compareFingerprints({
    required String fingerprint1Path,
    required String fingerprint2Path,
  }) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('compareFingerprints', {
          'fingerprint1Path': fingerprint1Path,
          'fingerprint2Path': fingerprint2Path,
        })
      );
      
      if (result['success'] == true) {
        return ComparisonResult(
          success: true,
          isMatch: result['isMatch'] ?? false,
          matchScore: (result['matchScore'] ?? 0.0).toDouble(),
          threshold: (result['threshold'] ?? 0.75).toDouble(),
          confidence: result['confidence'] ?? 'Unknown',
          comparedAt: result['comparedAt'] ?? '',
          errorMessage: null,
        );
      } else {
        return ComparisonResult(
          success: false,
          isMatch: false,
          matchScore: 0.0,
          threshold: 0.75,
          confidence: 'Unknown',
          comparedAt: '',
          errorMessage: result['error'] ?? 'Unknown error',
        );
      }
    } on PlatformException catch (e) {
      return ComparisonResult(
        success: false,
        isMatch: false,
        matchScore: 0.0,
        threshold: 0.75,
        confidence: 'Unknown',
        comparedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Extract face template from image
  static Future<TemplateResult> extractFaceTemplate(String facePath) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('extractFaceTemplate', {
          'facePath': facePath,
        })
      );
      
      return TemplateResult(
        success: result['success'] ?? false,
        templateSize: result['templateSize'] ?? 0,
        quality: result['quality'] ?? 'Unknown',
        extractedAt: result['extractedAt'] ?? '',
        errorMessage: result['success'] == true ? null : (result['error'] ?? 'Unknown error'),
      );
    } on PlatformException catch (e) {
      return TemplateResult(
        success: false,
        templateSize: 0,
        quality: 'Unknown',
        extractedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Extract fingerprint template from image
  static Future<FingerprintTemplateResult> extractFingerprintTemplate(String fingerprintPath) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('extractFingerprintTemplate', {
          'fingerprintPath': fingerprintPath,
        })
      );
      
      return FingerprintTemplateResult(
        success: result['success'] ?? false,
        templateSize: result['templateSize'] ?? 0,
        minutiaeCount: result['minutiaeCount'] ?? 0,
        quality: result['quality'] ?? 'Unknown',
        extractedAt: result['extractedAt'] ?? '',
        errorMessage: result['success'] == true ? null : (result['error'] ?? 'Unknown error'),
      );
    } on PlatformException catch (e) {
      return FingerprintTemplateResult(
        success: false,
        templateSize: 0,
        minutiaeCount: 0,
        quality: 'Unknown',
        extractedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Validate cryptograph integrity
  static Future<ValidationResult> validateCryptographIntegrity(String cryptographPath) async {
    if (!_isInitialized) {
      throw Exception('AirSnap not initialized. Call initializeAirSnap() first.');
    }
    
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('validateCryptographIntegrity', {
          'cryptographPath': cryptographPath,
        })
      );
      
      return ValidationResult(
        success: result['success'] ?? false,
        isValid: result['isValid'] ?? false,
        integrityScore: (result['integrityScore'] ?? 0.0).toDouble(),
        validatedAt: result['validatedAt'] ?? '',
        errorMessage: result['success'] == true ? null : (result['error'] ?? 'Unknown error'),
      );
    } on PlatformException catch (e) {
      return ValidationResult(
        success: false,
        isValid: false,
        integrityScore: 0.0,
        validatedAt: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Get cryptograph metadata
  static Future<MetadataResult> getCryptographMetadata(String cryptographPath) async {
    try {
      final Map<String, dynamic> result = Map<String, dynamic>.from(
        await _channel.invokeMethod('getCryptographMetadata', {
          'cryptographPath': cryptographPath,
        })
      );
      
      return MetadataResult(
        success: result['success'] ?? false,
        fileName: result['fileName'] ?? '',
        fileSize: result['fileSize'] ?? 0,
        lastModified: result['lastModified'] ?? '',
        path: result['path'] ?? '',
        errorMessage: result['success'] == true ? null : (result['error'] ?? 'Unknown error'),
      );
    } on PlatformException catch (e) {
      return MetadataResult(
        success: false,
        fileName: '',
        fileSize: 0,
        lastModified: '',
        path: '',
        errorMessage: 'Platform error: ${e.message}',
      );
    }
  }
  
  /// Check if AirSnap is initialized
  static bool get isInitialized => _isInitialized;
}
