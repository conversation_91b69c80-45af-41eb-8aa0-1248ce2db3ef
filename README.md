# AirSnap Cryptograph System

A comprehensive Java application for generating, scanning, and comparing biometric cryptographs using AirSnap libraries.

## Features

- **Cryptograph Generation**: Generate cryptographs from demographic information, face images, and fingerprints
- **Cryptograph Scanning**: Scan existing cryptographs to extract demographic and biometric information
- **Biometric Comparison**: Compare fingerprints, face templates, and complete cryptographs
- **History and Audit**: Track all operations with comprehensive logging and statistics
- **JavaFX UI**: User-friendly desktop application with tabbed interface

## System Architecture

### Core Components

1. **Database Layer** (`DatabaseManager`)
   - SQLite database for storing cryptographs, demographics, and audit logs
   - Comprehensive schema with proper relationships and indexing

2. **Service Layer**
   - `CryptographGenerationService`: Generate cryptographs using T5CryptographClient
   - `CryptographScanningService`: Scan cryptographs using CryptographReader
   - `BiometricComparisonService`: Compare biometrics using OmnimatchUtil
   - `HistoryAndAuditService`: Track operations and provide statistics

3. **Data Access Layer**
   - `CryptographDAO`: CRUD operations for cryptographs
   - `DemographicDataService`: Manage demographic information
   - `BiometricDataService`: Handle biometric data operations

4. **UI Layer**
   - JavaFX application with tabbed interface
   - Background task execution for long-running operations
   - Real-time progress indicators and result displays

### AirSnap Library Integration

The system integrates with the following AirSnap libraries:

- **T5CryptographClient**: For cryptograph generation
- **CryptographReader**: For cryptograph scanning and validation
- **OmnimatchUtil**: For biometric template comparison
- **TLVDecode**: For Tag-Length-Value data processing

## Prerequisites

### Required Libraries

The following AirSnap .aar files must be present in the `libs/` directory:

- `t5cryptographclient-release.aar`
- `cryptographreader-release.aar`
- `omnimatchutil-release.aar`
- `tlvdecode-release.aar`

### System Requirements

- Java 11 or higher
- JavaFX 17.0.2
- SQLite support
- Native library support for JNI

### Maven Dependencies

```xml
<dependencies>
    <dependency>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-controls</artifactId>
        <version>17.0.2</version>
    </dependency>
    <dependency>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-fxml</artifactId>
        <version>17.0.2</version>
    </dependency>
    <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>3.42.0.0</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.2</version>
    </dependency>
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>2.0.7</version>
    </dependency>
</dependencies>
```

## Building and Running

### Compilation

```bash
mvn clean compile
```

### Running Tests

```bash
mvn test
```

### Running the Application

```bash
mvn javafx:run
```

Or run the main class directly:
```bash
java -cp target/classes com.airsnap.cryptograph.CryptographApplication
```

## Usage

### 1. Generate Cryptograph

1. Open the "Generate Cryptograph" tab
2. Fill in demographic information (name, date of birth, etc.)
3. Select face image and fingerprint image files
4. Click "Generate Cryptograph"
5. View the generated cryptograph details in the results area

### 2. Scan Cryptograph

1. Open the "Scan Cryptograph" tab
2. Select a cryptograph file to scan
3. Click "Scan Cryptograph"
4. View the extracted demographic and biometric information

### 3. Compare Biometrics

1. Open the "Compare Biometrics" tab
2. Select two biometric files (fingerprints, face templates, or cryptographs)
3. Choose the comparison type
4. Click "Perform Comparison"
5. View the comparison results and match score

### 4. View History

1. Open the "View History" tab
2. Select history type (Generation, Scan, Comparison, or Audit Log)
3. Optionally set date range filters
4. Click "Refresh" to load history data
5. View detailed operation history and system statistics

## Database Schema

The system uses SQLite with the following main tables:

- `cryptographs`: Store generated cryptographs
- `demographic_info`: Personal information
- `biometric_data`: Face and fingerprint data
- `scan_history`: Record of scan operations
- `comparison_history`: Record of comparison operations
- `audit_log`: Comprehensive operation logging

## Important Notes

### Native Library Requirements

This system requires native JNI implementations for the AirSnap libraries. The current implementation includes:

1. **Java Native Interface (JNI) declarations** in `AirSnapLibraryWrapper.java`
2. **Mock implementations** for development and testing
3. **Proper error handling** for missing native libraries

### Production Deployment

For production use, you must:

1. **Compile native JNI libraries** from the AirSnap .aar files for your target platform
2. **Obtain proper AirSnap licenses** and configure license keys
3. **Deploy native libraries** to the system library path
4. **Configure proper security** for biometric data handling

### Security Considerations

- All biometric data is stored securely in the database
- Cryptographs are validated using SHA-256 hashes
- Comprehensive audit logging tracks all operations
- Proper error handling prevents information leakage

## Testing

The system includes comprehensive tests:

- **Unit tests** for individual services
- **Integration tests** for database operations
- **System tests** for end-to-end functionality
- **Mock implementations** for development without native libraries

Run tests with:
```bash
mvn test
```

## Troubleshooting

### Common Issues

1. **Native Library Not Found**: Ensure JNI libraries are compiled and in the library path
2. **License Issues**: Verify AirSnap license keys are properly configured
3. **Database Errors**: Check SQLite permissions and disk space
4. **JavaFX Issues**: Ensure JavaFX runtime is properly installed

### Logging

The system uses SLF4J for logging. Check logs for detailed error information and operation tracking.

## Support

For issues related to:
- **AirSnap libraries**: Contact AirSnap support
- **System implementation**: Check the comprehensive test suite and documentation
- **Database issues**: Verify SQLite configuration and permissions

## License

This system integrates with proprietary AirSnap libraries. Ensure proper licensing before production use.
