# AirSnap Computer Vision Java Integration

A comprehensive Java application that integrates AirSnap biometric libraries with computer vision functionality, featuring a JavaFX-based user interface for testing and demonstration.

## Features

### Computer Vision Service
- **Object Detection**: Detect and classify objects in images
- **Feature Extraction**: Extract SIFT, SURF, ORB, Harris Corners, FAST, and BRIEF features
- **Image Enhancement**: Brightness adjustment, contrast enhancement, noise reduction, sharpening
- **Image Segmentation**: Semantic, instance, panoptic, watershed, and region growing segmentation
- **Neural Network Processing**: Load and process images with neural network models
- **Image Filtering**: Apply various image filters (Gaussian blur, sharpen, etc.)
- **Image Similarity**: Calculate similarity using cosine, Euclidean, Manhattan, correlation, and histogram metrics
- **OCR (Optical Character Recognition)**: Extract text from images
- **Processing Statistics**: Track and display processing performance metrics

### JavaFX User Interface
- **Image Loading**: Load images from file system
- **Real-time Processing**: Process images with various computer vision algorithms
- **Results Display**: View original and processed images side by side
- **Interactive Controls**: Easy-to-use buttons for all computer vision functions
- **Results Logging**: Detailed text output of all processing results
- **Service Management**: Initialize and cleanup computer vision services

## Project Structure

```
src/
├── main/java/com/airsnap/integration/
│   ├── interfaces/
│   │   ├── ComputerVisionService.java      # Main CV service interface
│   │   └── AirSnapCoreService.java         # Core AirSnap service interface
│   ├── impl/
│   │   ├── ComputerVisionServiceImpl.java  # CV service implementation
│   │   └── AirSnapCoreServiceImpl.java     # Core service implementation
│   ├── AirSnapDesktopApp.java              # Main JavaFX application
│   └── TestRunner.java                     # Command-line test runner
├── test/java/com/airsnap/integration/
│   ├── ComputerVisionServiceTest.java      # Comprehensive unit tests
│   └── TestImageGenerator.java             # Test image generation utility
└── main/resources/
    └── styles.css                          # JavaFX application styling
```

## Requirements

- Java 11 or higher
- JavaFX SDK (for UI application)
- Maven (optional, for dependency management)

## Quick Start

### 1. Run Tests (Command Line)

```bash
# Windows
run-tests.bat

# Linux/Mac
chmod +x run-tests.sh
./run-tests.sh
```

### 2. Run JavaFX Application

#### With Maven:
```bash
mvn clean compile
mvn javafx:run
```

#### With Java directly:
```bash
# Download JavaFX SDK from https://openjfx.io/
# Extract to a directory (e.g., /path/to/javafx-sdk)

java --module-path /path/to/javafx-sdk/lib \
     --add-modules javafx.controls,javafx.fxml,javafx.swing \
     -cp target/classes \
     com.airsnap.integration.AirSnapDesktopApp
```

## Usage

### Command Line Testing

The `TestRunner` class provides a comprehensive test of all computer vision functionality:

```java
java -cp target/classes com.airsnap.integration.TestRunner
```

This will:
1. Create sample test images
2. Initialize the computer vision service
3. Test all major functions (object detection, feature extraction, etc.)
4. Display results and performance metrics

### JavaFX GUI Application

1. **Launch the application**
2. **Initialize Service**: Click "Service" → "Initialize Service"
3. **Load Image**: Click "File" → "Load Image" and select an image file
4. **Process Image**: Use the control panel buttons to apply various computer vision operations:
   - **Detect Objects**: Find and classify objects in the image
   - **Extract Features**: Extract feature vectors using different algorithms
   - **Enhance Image**: Apply image enhancement techniques
   - **Segment Image**: Perform image segmentation
   - **Perform OCR**: Extract text from the image
   - **Neural Network**: Process with neural network models
   - **Apply Filters**: Apply image filters
   - **Show Statistics**: Display processing performance metrics

5. **View Results**: 
   - Processed images appear in the right panel
   - Detailed results are shown in the text area below
   - Status updates appear in the bottom status bar

## API Reference

### ComputerVisionService Interface

```java
// Initialize the service
boolean initialize();

// Object detection
List<ObjectDetectionResult> detectObjects(BufferedImage image);

// Feature extraction
float[] extractImageFeatures(BufferedImage image, FeatureType featureType);

// Image enhancement
BufferedImage enhanceImageQuality(BufferedImage image, EnhancementType enhancementType);

// Image segmentation
BufferedImage performImageSegmentation(BufferedImage image, SegmentationType segmentationType);

// Neural network processing
NeuralNetworkResult processWithNeuralNetwork(BufferedImage image, String modelName);

// OCR
String performOCR(BufferedImage image);

// Image similarity
double calculateImageSimilarity(BufferedImage image1, BufferedImage image2, SimilarityMetric metric);

// Statistics
ProcessingStatistics getProcessingStatistics();
```

### Supported Enums

- **FeatureType**: SIFT, SURF, ORB, HARRIS_CORNERS, FAST, BRIEF
- **EnhancementType**: NOISE_REDUCTION, SHARPENING, CONTRAST_ENHANCEMENT, BRIGHTNESS_ADJUSTMENT, HISTOGRAM_EQUALIZATION
- **SegmentationType**: SEMANTIC, INSTANCE, PANOPTIC, WATERSHED, REGION_GROWING
- **SimilarityMetric**: COSINE, EUCLIDEAN, MANHATTAN, CORRELATION, HISTOGRAM
- **ModelType**: CLASSIFICATION, DETECTION, SEGMENTATION, FACE_RECOGNITION, FEATURE_EXTRACTION

## Testing

The project includes comprehensive unit tests covering all functionality:

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=ComputerVisionServiceTest
```

### Test Coverage

- Service initialization and cleanup
- All computer vision operations
- Error handling and edge cases
- Performance and statistics tracking
- Image format compatibility

## Development

### Adding New Features

1. **Define Interface**: Add new methods to `ComputerVisionService.java`
2. **Implement**: Add implementation in `ComputerVisionServiceImpl.java`
3. **Add UI Controls**: Update `AirSnapDesktopApp.java` with new buttons/controls
4. **Write Tests**: Add test cases in `ComputerVisionServiceTest.java`
5. **Update Documentation**: Update this README with new features

### Code Style

- Follow Java naming conventions
- Use meaningful variable and method names
- Add comprehensive JavaDoc comments
- Include error handling and logging
- Write unit tests for all new functionality

## Troubleshooting

### Common Issues

1. **JavaFX not found**: Download and install JavaFX SDK, ensure correct module path
2. **Image loading fails**: Check image format support (PNG, JPG, GIF, BMP)
3. **Service initialization fails**: Check logs for detailed error messages
4. **Performance issues**: Monitor processing statistics and adjust image sizes

### Logging

The application uses SLF4J with Logback for logging. Check console output for detailed information about:
- Service initialization
- Processing operations
- Error conditions
- Performance metrics

## License

This project is part of the AirSnap biometric system integration suite.

## Support

For technical support or questions about this integration, please refer to the AirSnap documentation or contact the development team.
