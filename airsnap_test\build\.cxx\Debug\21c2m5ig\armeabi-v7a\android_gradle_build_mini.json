{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\My Projects\\java intergration\\airsnap_test\\build\\.cxx\\Debug\\21c2m5ig\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\My Projects\\java intergration\\airsnap_test\\build\\.cxx\\Debug\\21c2m5ig\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}