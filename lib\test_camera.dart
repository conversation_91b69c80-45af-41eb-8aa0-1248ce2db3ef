import 'dart:io';
import 'package:flutter/material.dart';
import 'services/camera_service.dart';
import 'widgets/camera_capture_widget.dart';
import 'widgets/face_capture_widget.dart';
import 'widgets/fingerprint_capture_widget.dart';

class CameraTestApp extends StatelessWidget {
  const CameraTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Camera Integration Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const CameraTestScreen(),
    );
  }
}

class CameraTestScreen extends StatefulWidget {
  const CameraTestScreen({super.key});

  @override
  State<CameraTestScreen> createState() => _CameraTestScreenState();
}

class _CameraTestScreenState extends State<CameraTestScreen> {
  File? _capturedImage;
  String _status = 'Ready to test camera';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    setState(() {
      _status = 'Initializing camera...';
    });

    try {
      await CameraService.initialize();
      setState(() {
        _status = 'Camera initialized successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Camera initialization failed: $e';
      });
    }
  }

  Future<void> _testBasicCamera() async {
    try {
      setState(() {
        _status = 'Opening basic camera...';
      });

      final File? imageFile = await Navigator.push<File>(
        context,
        MaterialPageRoute(
          builder: (context) => CameraCaptureWidget(
            title: 'Basic Camera Test',
            subtitle: 'Test basic camera functionality',
            onImageCaptured: (File file) => file,
          ),
        ),
      );

      if (imageFile != null) {
        setState(() {
          _capturedImage = imageFile;
          _status = 'Image captured successfully: ${imageFile.path}';
        });
      } else {
        setState(() {
          _status = 'No image captured';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Camera test failed: $e';
      });
    }
  }

  Future<void> _testFaceCapture() async {
    try {
      setState(() {
        _status = 'Opening face capture...';
      });

      final File? faceFile = await showDialog<File>(
        context: context,
        builder: (context) => FaceCaptureDialog(
          onFaceCaptured: (File file) => file,
        ),
      );

      if (faceFile != null) {
        setState(() {
          _capturedImage = faceFile;
          _status = 'Face captured successfully: ${faceFile.path}';
        });
      } else {
        setState(() {
          _status = 'No face captured';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Face capture failed: $e';
      });
    }
  }

  Future<void> _testFingerprintCapture() async {
    try {
      setState(() {
        _status = 'Opening fingerprint capture...';
      });

      final Map<String, dynamic>? result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => FingerprintCaptureDialog(
          onFingerprintCaptured: (File file, FingerType type) => {
            'file': file,
            'type': type,
          },
        ),
      );

      if (result != null) {
        final File fingerprintFile = result['file'] as File;
        final FingerType fingerType = result['type'] as FingerType;
        
        setState(() {
          _capturedImage = fingerprintFile;
          _status = 'Fingerprint captured: ${fingerType.displayName} - ${fingerprintFile.path}';
        });
      } else {
        setState(() {
          _status = 'No fingerprint captured';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Fingerprint capture failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camera Integration Test'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Camera Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        color: _status.contains('failed') || _status.contains('error')
                            ? Colors.red
                            : _status.contains('successfully')
                                ? Colors.green
                                : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test buttons
            ElevatedButton.icon(
              onPressed: _testBasicCamera,
              icon: const Icon(Icons.camera_alt),
              label: const Text('Test Basic Camera'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 12),

            ElevatedButton.icon(
              onPressed: _testFaceCapture,
              icon: const Icon(Icons.face),
              label: const Text('Test Face Capture'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 12),

            ElevatedButton.icon(
              onPressed: _testFingerprintCapture,
              icon: const Icon(Icons.fingerprint),
              label: const Text('Test Fingerprint Capture'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 16),

            // Image preview
            if (_capturedImage != null) ...[
              const Text(
                'Captured Image:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _capturedImage!,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'No image captured yet',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Test main function
void main() {
  runApp(const CameraTestApp());
}
