package com.airsnap.integration.impl;

import com.airsnap.integration.interfaces.UtilityService;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

/**
 * Implementation of Utility Service
 * Simulates OmnimatchUtil and TLVDecode functionality
 */
public class UtilityServiceImpl implements UtilityService {
    
    private void log(String message) { System.out.println("[" + getClass().getSimpleName() + "] " + message); }
    private final Random random = new Random();
    private boolean initialized = false;
    private final List<String> operationHistory = new ArrayList<>();
    
    @Override
    public boolean initialize() {
        log("Initializing Utility Service...");
        try {
            Thread.sleep(500);
            initialized = true;
            log("Utility Service initialized successfully");
            return true;
        } catch (InterruptedException e) {
            log("Failed to initialize Utility Service");
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    @Override
    public List<TLVElement> parseTLV(byte[] tlvData) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Parsing TLV data of {} bytes");
        operationHistory.add("parseTLV");
        
        List<TLVElement> elements = new ArrayList<>();
        ByteBuffer buffer = ByteBuffer.wrap(tlvData);
        
        while (buffer.remaining() >= 2) {
            int tag = buffer.get() & 0xFF;
            int length = buffer.get() & 0xFF;
            
            if (buffer.remaining() < length) {
                break; // Not enough data
            }
            
            byte[] value = new byte[length];
            buffer.get(value);
            elements.add(new TLVElement(tag, length, value));
        }
        
        log("Parsed {} TLV elements"));
        return elements;
    }
    
    @Override
    public byte[] encodeTLV(List<TLVElement> elements) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Encoding {} TLV elements"));
        operationHistory.add("encodeTLV");
        
        ByteBuffer buffer = ByteBuffer.allocate(elements.size() * 256); // Estimate size
        
        for (TLVElement element : elements) {
            buffer.put((byte) element.getTag());
            buffer.put((byte) element.getLength());
            buffer.put(element.getValue());
        }
        
        byte[] result = new byte[buffer.position()];
        buffer.rewind();
        buffer.get(result);
        
        log("Encoded TLV data: {} bytes");
        return result;
    }
    
    @Override
    public TLVElement findTLVElement(byte[] tlvData, int tag) {
        List<TLVElement> elements = parseTLV(tlvData);
        return elements.stream()
                .filter(element -> element.getTag() == tag)
                .findFirst()
                .orElse(null);
    }
    
    @Override
    public OmniMatchResult performOmniMatch(byte[] template1, byte[] template2, MatchType matchType) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Performing {} match between templates");
        operationHistory.add("performOmniMatch");
        
        double score = calculateMatchScore(template1, template2, matchType);
        boolean matched = score > 0.7; // Threshold
        
        Map<String, Object> details = new HashMap<>();
        details.put("template1_size", template1.length);
        details.put("template2_size", template2.length);
        details.put("algorithm", matchType.toString());
        details.put("processing_time", random.nextInt(100) + 50);
        
        log("Match result: {} (score: {})");
        return new OmniMatchResult(matched, score, matchType, details);
    }
    
    @Override
    public byte[] calculateChecksum(byte[] data, String algorithm) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Calculating {} checksum for {} bytes");
        operationHistory.add("calculateChecksum");
        
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            return digest.digest(data);
        } catch (Exception e) {
            log("Failed to calculate checksum");
            throw new RuntimeException("Checksum calculation failed", e);
        }
    }
    
    @Override
    public boolean validateDataIntegrity(byte[] data, byte[] expectedChecksum, String algorithm) {
        byte[] actualChecksum = calculateChecksum(data, algorithm);
        return Arrays.equals(actualChecksum, expectedChecksum);
    }
    
    @Override
    public byte[] convertDataFormat(byte[] data, DataFormat fromFormat, DataFormat toFormat) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Converting data from {} to {}");
        operationHistory.add("convertDataFormat");
        
        // Simulate format conversion
        if (fromFormat == toFormat) {
            return data;
        }
        
        switch (toFormat) {
            case BASE64:
                return Base64.getEncoder().encode(data);
            case HEX:
                return bytesToHex(data).getBytes();
            case RAW:
                if (fromFormat == DataFormat.BASE64) {
                    return Base64.getDecoder().decode(data);
                } else if (fromFormat == DataFormat.HEX) {
                    return hexToBytes(new String(data));
                }
                return data;
            default:
                return data;
        }
    }
    
    @Override
    public byte[] compressData(byte[] data, String algorithm) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Compressing {} bytes with {}");
        operationHistory.add("compressData");
        
        try {
            Deflater deflater = new Deflater();
            deflater.setInput(data);
            deflater.finish();
            
            byte[] buffer = new byte[data.length];
            int compressedLength = deflater.deflate(buffer);
            deflater.end();
            
            return Arrays.copyOf(buffer, compressedLength);
        } catch (Exception e) {
            log("Failed to compress data");
            throw new RuntimeException("Compression failed", e);
        }
    }
    
    @Override
    public byte[] decompressData(byte[] compressedData, String algorithm) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        log("Decompressing {} bytes with {}");
        operationHistory.add("decompressData");
        
        try {
            Inflater inflater = new Inflater();
            inflater.setInput(compressedData);
            
            byte[] buffer = new byte[compressedData.length * 4]; // Estimate
            int decompressedLength = inflater.inflate(buffer);
            inflater.end();
            
            return Arrays.copyOf(buffer, decompressedLength);
        } catch (Exception e) {
            log("Failed to decompress data");
            throw new RuntimeException("Decompression failed", e);
        }
    }
    
    @Override
    public UtilityReport generateReport(List<String> operations) {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total_operations", operationHistory.size());
        statistics.put("unique_operations", new HashSet<>(operationHistory).size());
        statistics.put("most_used_operation", getMostFrequentOperation());
        
        List<String> errors = new ArrayList<>();
        // Simulate some errors
        if (random.nextDouble() < 0.1) {
            errors.add("Minor processing delay detected");
        }
        
        return new UtilityReport(System.currentTimeMillis(), new ArrayList<>(operationHistory), statistics, errors);
    }
    
    @Override
    public void cleanup() {
        log("Cleaning up Utility Service");
        operationHistory.clear();
        initialized = false;
    }
    
    private double calculateMatchScore(byte[] template1, byte[] template2, MatchType matchType) {
        if (template1.length != template2.length) {
            return 0.0;
        }
        
        int matches = 0;
        for (int i = 0; i < template1.length; i++) {
            if (template1[i] == template2[i]) {
                matches++;
            }
        }
        
        double baseScore = (double) matches / template1.length;
        
        // Adjust based on match type
        switch (matchType) {
            case EXACT:
                return baseScore;
            case FUZZY:
                return Math.min(1.0, baseScore * 1.2);
            case TEMPLATE:
                return Math.min(1.0, baseScore * 1.1);
            case BIOMETRIC:
                return Math.min(1.0, baseScore * 1.15);
            default:
                return baseScore;
        }
    }
    
    private String getMostFrequentOperation() {
        return operationHistory.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("none");
    }
    
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
    
    private byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
}
