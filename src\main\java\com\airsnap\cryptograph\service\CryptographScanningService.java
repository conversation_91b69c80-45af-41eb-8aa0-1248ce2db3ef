package com.airsnap.cryptograph.service;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.DemographicInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for scanning and extracting information from cryptographs
 */
public class CryptographScanningService {
    private static final Logger logger = LoggerFactory.getLogger(CryptographScanningService.class);
    
    private final AirSnapLibraryWrapper airSnapWrapper;
    private final DatabaseManager dbManager;
    private final ObjectMapper objectMapper;
    
    public CryptographScanningService(DatabaseManager dbManager) {
        this.airSnapWrapper = new AirSnapLibraryWrapper();
        this.dbManager = dbManager;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Scan a cryptograph and extract all information
     */
    public ScanResult scanCryptograph(byte[] cryptographData) {
        logger.info("Starting cryptograph scan (Size: {} bytes)", cryptographData.length);
        
        try {
            // 1. Scan the cryptograph using AirSnap library
            String scanResultJson = airSnapWrapper.safeScanCryptograph(cryptographData);
            
            if (scanResultJson == null || scanResultJson.isEmpty()) {
                return new ScanResult(false, "Failed to scan cryptograph - no data returned", null, null, null, 0.0);
            }
            
            // 2. Parse the scan result
            JsonNode scanResultNode = objectMapper.readTree(scanResultJson);
            
            // 3. Extract demographic information
            DemographicInfo demographicInfo = extractDemographicInfo(cryptographData);
            
            // 4. Extract biometric templates
            byte[] biometricTemplates = airSnapWrapper.extractBiometricTemplates(cryptographData);
            
            // 5. Calculate confidence score
            double confidenceScore = calculateConfidenceScore(scanResultNode);
            
            // 6. Create scan result
            ScanResult result = new ScanResult(
                true, 
                "Cryptograph scanned successfully", 
                demographicInfo, 
                biometricTemplates, 
                scanResultJson, 
                confidenceScore
            );
            
            // 7. Log scan history
            logScanHistory(cryptographData, result);
            
            logger.info("Cryptograph scan completed successfully with confidence: {}", confidenceScore);
            return result;
            
        } catch (Exception e) {
            logger.error("Error scanning cryptograph", e);
            ScanResult errorResult = new ScanResult(false, "Scan failed: " + e.getMessage(), null, null, null, 0.0);
            
            // Log failed scan
            try {
                logScanHistory(cryptographData, errorResult);
            } catch (Exception logError) {
                logger.error("Failed to log scan history", logError);
            }
            
            return errorResult;
        }
    }
    
    /**
     * Extract demographic information from cryptograph
     */
    public DemographicInfo extractDemographicInfo(byte[] cryptographData) {
        try {
            String demographicJson = airSnapWrapper.extractDemographicInfo(cryptographData);
            
            if (demographicJson == null || demographicJson.isEmpty()) {
                logger.warn("No demographic information found in cryptograph");
                return null;
            }
            
            // Parse JSON and create DemographicInfo object
            JsonNode demographicNode = objectMapper.readTree(demographicJson);
            
            DemographicInfo demographicInfo = new DemographicInfo();
            demographicInfo.setFirstName(getJsonStringValue(demographicNode, "firstName"));
            demographicInfo.setLastName(getJsonStringValue(demographicNode, "lastName"));
            demographicInfo.setGender(getJsonStringValue(demographicNode, "gender"));
            demographicInfo.setNationality(getJsonStringValue(demographicNode, "nationality"));
            demographicInfo.setDocumentNumber(getJsonStringValue(demographicNode, "documentNumber"));
            demographicInfo.setDocumentType(getJsonStringValue(demographicNode, "documentType"));
            
            // Parse date of birth
            String dobString = getJsonStringValue(demographicNode, "dateOfBirth");
            if (dobString != null && !dobString.isEmpty()) {
                try {
                    demographicInfo.setDateOfBirth(LocalDate.parse(dobString));
                } catch (Exception e) {
                    logger.warn("Failed to parse date of birth: {}", dobString);
                }
            }
            
            logger.info("Extracted demographic info for: {}", demographicInfo.getFullName());
            return demographicInfo;
            
        } catch (Exception e) {
            logger.error("Error extracting demographic information", e);
            return null;
        }
    }
    
    /**
     * Extract and decode TLV data from cryptograph
     */
    public Map<String, Object> decodeTLVData(byte[] cryptographData) {
        try {
            String tlvJson = airSnapWrapper.decodeTLV(cryptographData);
            
            if (tlvJson == null || tlvJson.isEmpty()) {
                logger.warn("No TLV data found in cryptograph");
                return new HashMap<>();
            }
            
            JsonNode tlvNode = objectMapper.readTree(tlvJson);
            Map<String, Object> tlvData = objectMapper.convertValue(tlvNode, Map.class);
            
            logger.info("Decoded TLV data with {} fields", tlvData.size());
            return tlvData;
            
        } catch (Exception e) {
            logger.error("Error decoding TLV data", e);
            return new HashMap<>();
        }
    }
    
    /**
     * Validate cryptograph integrity
     */
    public ValidationResult validateCryptograph(byte[] cryptographData) {
        try {
            // 1. Basic data validation
            if (cryptographData == null || cryptographData.length == 0) {
                return new ValidationResult(false, "Cryptograph data is empty", 0.0);
            }
            
            // 2. Try to scan the cryptograph
            String scanResult = airSnapWrapper.safeScanCryptograph(cryptographData);
            if (scanResult == null) {
                return new ValidationResult(false, "Cryptograph is not readable", 0.0);
            }
            
            // 3. Validate structure
            JsonNode scanNode = objectMapper.readTree(scanResult);
            double structureScore = validateStructure(scanNode);
            
            // 4. Validate demographic data
            DemographicInfo demographicInfo = extractDemographicInfo(cryptographData);
            double demographicScore = validateDemographicData(demographicInfo);
            
            // 5. Validate biometric data
            byte[] biometricTemplates = airSnapWrapper.extractBiometricTemplates(cryptographData);
            double biometricScore = validateBiometricData(biometricTemplates);
            
            // 6. Calculate overall validation score
            double overallScore = (structureScore + demographicScore + biometricScore) / 3.0;
            
            boolean isValid = overallScore >= 0.7; // 70% threshold
            String message = isValid ? "Cryptograph is valid" : "Cryptograph validation failed";
            
            logger.info("Cryptograph validation completed: {} (Score: {})", message, overallScore);
            return new ValidationResult(isValid, message, overallScore);
            
        } catch (Exception e) {
            logger.error("Error validating cryptograph", e);
            return new ValidationResult(false, "Validation error: " + e.getMessage(), 0.0);
        }
    }
    
    /**
     * Log scan history to database
     */
    private void logScanHistory(byte[] cryptographData, ScanResult result) throws SQLException {
        String cryptographId = "UNKNOWN";
        try {
            // Try to extract cryptograph ID from the data
            String scanJson = result.getRawScanData();
            if (scanJson != null) {
                JsonNode scanNode = objectMapper.readTree(scanJson);
                cryptographId = getJsonStringValue(scanNode, "cryptographId");
            }
        } catch (Exception e) {
            logger.warn("Could not extract cryptograph ID for logging");
        }
        
        String sql = """
            INSERT INTO scan_history (cryptograph_id, scan_result, extracted_data, 
                                    scan_confidence, scan_method)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, cryptographId);
            stmt.setString(2, result.isSuccess() ? "SUCCESS" : "FAILED");
            stmt.setString(3, result.getRawScanData());
            stmt.setDouble(4, result.getConfidenceScore());
            stmt.setString(5, "AIRSNAP_CRYPTOGRAPH_READER");
            
            stmt.executeUpdate();
            
            // Log audit
            dbManager.logAudit("SCAN", "CRYPTOGRAPH", cryptographId, 
                             "SYSTEM", "Cryptograph scanned", result.isSuccess(), 
                             result.isSuccess() ? null : result.getMessage());
        }
    }
    
    // Helper methods
    
    private String getJsonStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }
    
    private double calculateConfidenceScore(JsonNode scanResult) {
        // Implementation would depend on the actual structure of scan results
        // This is a placeholder implementation
        try {
            JsonNode confidenceNode = scanResult.get("confidence");
            if (confidenceNode != null) {
                return confidenceNode.asDouble();
            }
            
            // Calculate based on available data quality
            double score = 0.5; // Base score
            
            if (scanResult.has("demographicInfo")) score += 0.2;
            if (scanResult.has("biometricData")) score += 0.2;
            if (scanResult.has("cryptographId")) score += 0.1;
            
            return Math.min(score, 1.0);
        } catch (Exception e) {
            return 0.5; // Default confidence
        }
    }
    
    private double validateStructure(JsonNode scanNode) {
        double score = 0.0;
        if (scanNode.has("cryptographId")) score += 0.3;
        if (scanNode.has("demographicInfo")) score += 0.3;
        if (scanNode.has("biometricData")) score += 0.4;
        return score;
    }
    
    private double validateDemographicData(DemographicInfo demographicInfo) {
        if (demographicInfo == null) return 0.0;
        
        double score = 0.0;
        if (demographicInfo.getFirstName() != null && !demographicInfo.getFirstName().isEmpty()) score += 0.2;
        if (demographicInfo.getLastName() != null && !demographicInfo.getLastName().isEmpty()) score += 0.2;
        if (demographicInfo.getDateOfBirth() != null) score += 0.2;
        if (demographicInfo.getDocumentNumber() != null && !demographicInfo.getDocumentNumber().isEmpty()) score += 0.2;
        if (demographicInfo.getNationality() != null && !demographicInfo.getNationality().isEmpty()) score += 0.2;
        
        return score;
    }
    
    private double validateBiometricData(byte[] biometricTemplates) {
        if (biometricTemplates == null || biometricTemplates.length == 0) return 0.0;
        
        // Basic validation based on data size
        if (biometricTemplates.length < 100) return 0.3; // Very small template
        if (biometricTemplates.length < 1000) return 0.6; // Small template
        if (biometricTemplates.length < 10000) return 0.8; // Medium template
        return 1.0; // Large template
    }
    
    // Inner classes for results
    
    public static class ScanResult {
        private final boolean success;
        private final String message;
        private final DemographicInfo demographicInfo;
        private final byte[] biometricTemplates;
        private final String rawScanData;
        private final double confidenceScore;
        private final LocalDateTime scanTime;
        
        public ScanResult(boolean success, String message, DemographicInfo demographicInfo, 
                         byte[] biometricTemplates, String rawScanData, double confidenceScore) {
            this.success = success;
            this.message = message;
            this.demographicInfo = demographicInfo;
            this.biometricTemplates = biometricTemplates;
            this.rawScanData = rawScanData;
            this.confidenceScore = confidenceScore;
            this.scanTime = LocalDateTime.now();
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public DemographicInfo getDemographicInfo() { return demographicInfo; }
        public byte[] getBiometricTemplates() { return biometricTemplates; }
        public String getRawScanData() { return rawScanData; }
        public double getConfidenceScore() { return confidenceScore; }
        public LocalDateTime getScanTime() { return scanTime; }
    }
    
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        private final double score;
        
        public ValidationResult(boolean valid, String message, double score) {
            this.valid = valid;
            this.message = message;
            this.score = score;
        }
        
        // Getters
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public double getScore() { return score; }
    }
}
