package com.airsnap.integration;

import com.airsnap.integration.impl.ComputerVisionServiceImpl;
import com.airsnap.integration.interfaces.ComputerVisionService.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

/**
 * Swing-based GUI for testing AirSnap Computer Vision functions
 * This runs without external dependencies
 */
public class SwingTestApp extends JFrame {
    
    private ComputerVisionServiceImpl cvService;
    private JTextArea outputArea;
    private JLabel imageLabel;
    private JLabel statusLabel;
    private BufferedImage currentImage;
    
    public SwingTestApp() {
        initializeGUI();
        initializeService();
    }
    
    private void initializeGUI() {
        setTitle("AirSnap Computer Vision Test Application");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // Create menu bar
        JMenuBar menuBar = new JMenuBar();
        JMenu fileMenu = new JMenu("File");
        JMenuItem loadImageItem = new JMenuItem("Load Test Image");
        JMenuItem exitItem = new JMenuItem("Exit");
        
        loadImageItem.addActionListener(e -> loadTestImage());
        exitItem.addActionListener(e -> System.exit(0));
        
        fileMenu.add(loadImageItem);
        fileMenu.addSeparator();
        fileMenu.add(exitItem);
        menuBar.add(fileMenu);
        setJMenuBar(menuBar);
        
        // Create main panels
        JPanel leftPanel = createControlPanel();
        JPanel centerPanel = createImagePanel();
        JPanel bottomPanel = createOutputPanel();
        
        add(leftPanel, BorderLayout.WEST);
        add(centerPanel, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
        
        // Status bar
        statusLabel = new JLabel("Ready - Load an image to begin testing");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        add(statusLabel, BorderLayout.NORTH);
        
        setSize(1000, 700);
        setLocationRelativeTo(null);
    }
    
    private JPanel createControlPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createTitledBorder("Computer Vision Functions"));
        panel.setPreferredSize(new Dimension(200, 0));
        
        // Service control buttons
        JButton initButton = new JButton("Initialize Service");
        JButton cleanupButton = new JButton("Cleanup Service");
        
        // Computer vision function buttons
        JButton detectObjectsBtn = new JButton("Detect Objects");
        JButton extractFeaturesBtn = new JButton("Extract Features");
        JButton enhanceImageBtn = new JButton("Enhance Image");
        JButton segmentImageBtn = new JButton("Segment Image");
        JButton performOCRBtn = new JButton("Perform OCR");
        JButton neuralNetworkBtn = new JButton("Neural Network");
        JButton applyFiltersBtn = new JButton("Apply Filters");
        JButton showStatsBtn = new JButton("Show Statistics");
        
        // Add action listeners
        initButton.addActionListener(e -> initializeService());
        cleanupButton.addActionListener(e -> cleanupService());
        detectObjectsBtn.addActionListener(e -> detectObjects());
        extractFeaturesBtn.addActionListener(e -> extractFeatures());
        enhanceImageBtn.addActionListener(e -> enhanceImage());
        segmentImageBtn.addActionListener(e -> segmentImage());
        performOCRBtn.addActionListener(e -> performOCR());
        neuralNetworkBtn.addActionListener(e -> processWithNeuralNetwork());
        applyFiltersBtn.addActionListener(e -> applyFilters());
        showStatsBtn.addActionListener(e -> showStatistics());
        
        // Add buttons to panel
        panel.add(Box.createVerticalStrut(10));
        panel.add(initButton);
        panel.add(cleanupButton);
        panel.add(Box.createVerticalStrut(20));
        panel.add(detectObjectsBtn);
        panel.add(extractFeaturesBtn);
        panel.add(enhanceImageBtn);
        panel.add(segmentImageBtn);
        panel.add(performOCRBtn);
        panel.add(neuralNetworkBtn);
        panel.add(applyFiltersBtn);
        panel.add(showStatsBtn);
        panel.add(Box.createVerticalGlue());
        
        return panel;
    }
    
    private JPanel createImagePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Image Display"));
        
        imageLabel = new JLabel("No image loaded", SwingConstants.CENTER);
        imageLabel.setPreferredSize(new Dimension(400, 300));
        imageLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JScrollPane scrollPane = new JScrollPane(imageLabel);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createOutputPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Output"));
        panel.setPreferredSize(new Dimension(0, 200));
        
        outputArea = new JTextArea(10, 50);
        outputArea.setEditable(false);
        outputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(outputArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void initializeService() {
        try {
            cvService = new ComputerVisionServiceImpl();
            boolean success = cvService.initialize();
            if (success) {
                appendOutput("✓ Computer Vision Service initialized successfully");
                statusLabel.setText("Service initialized - Ready for processing");
            } else {
                appendOutput("✗ Failed to initialize Computer Vision Service");
                statusLabel.setText("Service initialization failed");
            }
        } catch (Exception e) {
            appendOutput("✗ Error initializing service: " + e.getMessage());
            statusLabel.setText("Error: " + e.getMessage());
        }
    }
    
    private void cleanupService() {
        if (cvService != null) {
            cvService.cleanup();
            appendOutput("✓ Computer Vision Service cleaned up");
            statusLabel.setText("Service cleaned up");
        }
    }
    
    private void loadTestImage() {
        // Create a test image
        currentImage = new BufferedImage(400, 300, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = currentImage.createGraphics();
        
        // Draw a simple test pattern
        g2d.setColor(Color.BLUE);
        g2d.fillRect(0, 0, 400, 300);
        g2d.setColor(Color.WHITE);
        g2d.fillOval(50, 50, 100, 100);
        g2d.fillRect(200, 100, 150, 100);
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.BOLD, 24));
        g2d.drawString("TEST IMAGE", 120, 250);
        g2d.dispose();
        
        // Display the image
        ImageIcon icon = new ImageIcon(currentImage);
        imageLabel.setIcon(icon);
        imageLabel.setText("");
        
        appendOutput("✓ Test image loaded (400x300)");
        statusLabel.setText("Test image loaded - Ready for processing");
    }
    
    private void detectObjects() {
        if (!checkServiceAndImage()) return;
        
        try {
            var objects = cvService.detectObjects(currentImage);
            appendOutput("=== Object Detection Results ===");
            appendOutput("Detected " + objects.size() + " objects:");
            for (var obj : objects) {
                appendOutput("  - " + obj.getClassName() + " (" + 
                           String.format("%.1f%%", obj.getConfidence() * 100) + ")");
            }
            statusLabel.setText("Object detection completed - " + objects.size() + " objects found");
        } catch (Exception e) {
            appendOutput("✗ Error in object detection: " + e.getMessage());
        }
    }
    
    private void extractFeatures() {
        if (!checkServiceAndImage()) return;
        
        try {
            float[] features = cvService.extractImageFeatures(currentImage, FeatureType.SIFT);
            appendOutput("=== Feature Extraction Results ===");
            appendOutput("Extracted " + features.length + " SIFT features");
            appendOutput("Feature vector sample: [" + 
                       String.format("%.3f", features[0]) + ", " +
                       String.format("%.3f", features[1]) + ", " +
                       String.format("%.3f", features[2]) + ", ...]");
            statusLabel.setText("Feature extraction completed - " + features.length + " features");
        } catch (Exception e) {
            appendOutput("✗ Error in feature extraction: " + e.getMessage());
        }
    }
    
    private void enhanceImage() {
        if (!checkServiceAndImage()) return;
        
        try {
            BufferedImage enhanced = cvService.enhanceImageQuality(currentImage, EnhancementType.BRIGHTNESS_ADJUSTMENT);
            appendOutput("=== Image Enhancement Results ===");
            appendOutput("Image enhanced with brightness adjustment");
            appendOutput("Enhanced image size: " + enhanced.getWidth() + "x" + enhanced.getHeight());
            
            // Display enhanced image
            ImageIcon icon = new ImageIcon(enhanced);
            imageLabel.setIcon(icon);
            currentImage = enhanced;
            
            statusLabel.setText("Image enhancement completed");
        } catch (Exception e) {
            appendOutput("✗ Error in image enhancement: " + e.getMessage());
        }
    }
    
    private void segmentImage() {
        if (!checkServiceAndImage()) return;
        
        try {
            BufferedImage segmented = cvService.performImageSegmentation(currentImage, SegmentationType.SEMANTIC);
            appendOutput("=== Image Segmentation Results ===");
            appendOutput("Semantic segmentation completed");
            appendOutput("Segmented image size: " + segmented.getWidth() + "x" + segmented.getHeight());
            statusLabel.setText("Image segmentation completed");
        } catch (Exception e) {
            appendOutput("✗ Error in image segmentation: " + e.getMessage());
        }
    }
    
    private void performOCR() {
        if (!checkServiceAndImage()) return;
        
        try {
            String text = cvService.performOCR(currentImage);
            appendOutput("=== OCR Results ===");
            appendOutput("Detected text: \"" + text + "\"");
            statusLabel.setText("OCR completed");
        } catch (Exception e) {
            appendOutput("✗ Error in OCR: " + e.getMessage());
        }
    }
    
    private void processWithNeuralNetwork() {
        if (!checkServiceAndImage()) return;
        
        try {
            NeuralNetworkResult result = cvService.processWithNeuralNetwork(currentImage, "test-model.onnx");
            appendOutput("=== Neural Network Results ===");
            appendOutput("Processing time: " + result.getProcessingTime() + "ms");
            appendOutput("Predictions:");
            for (var prediction : result.getPredictions().entrySet()) {
                appendOutput("  " + prediction.getKey() + ": " + 
                           String.format("%.1f%%", prediction.getValue() * 100));
            }
            statusLabel.setText("Neural network processing completed");
        } catch (Exception e) {
            appendOutput("✗ Error in neural network processing: " + e.getMessage());
        }
    }
    
    private void applyFilters() {
        if (!checkServiceAndImage()) return;
        
        try {
            java.util.List<ImageFilter> filters = java.util.Arrays.asList(
                new ImageFilter("Gaussian Blur", new java.util.HashMap<>()));
            BufferedImage filtered = cvService.applyImageFilters(currentImage, filters);
            appendOutput("=== Image Filters Results ===");
            appendOutput("Applied Gaussian Blur filter");
            appendOutput("Filtered image size: " + filtered.getWidth() + "x" + filtered.getHeight());
            
            // Display filtered image
            ImageIcon icon = new ImageIcon(filtered);
            imageLabel.setIcon(icon);
            currentImage = filtered;
            
            statusLabel.setText("Image filters applied");
        } catch (Exception e) {
            appendOutput("✗ Error applying filters: " + e.getMessage());
        }
    }
    
    private void showStatistics() {
        if (cvService == null) {
            appendOutput("✗ Service not initialized");
            return;
        }
        
        try {
            ProcessingStatistics stats = cvService.getProcessingStatistics();
            appendOutput("=== Processing Statistics ===");
            appendOutput("Total processed images: " + stats.getTotalProcessedImages());
            appendOutput("Average processing time: " + String.format("%.2f ms", stats.getAverageProcessingTime()));
            appendOutput("Operation counts: " + stats.getOperationCounts().size() + " types");
            statusLabel.setText("Statistics displayed");
        } catch (Exception e) {
            appendOutput("✗ Error getting statistics: " + e.getMessage());
        }
    }
    
    private boolean checkServiceAndImage() {
        if (cvService == null) {
            appendOutput("✗ Please initialize the service first");
            return false;
        }
        if (currentImage == null) {
            appendOutput("✗ Please load an image first");
            return false;
        }
        return true;
    }
    
    private void appendOutput(String text) {
        outputArea.append(text + "\n");
        outputArea.setCaretPosition(outputArea.getDocument().getLength());
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // Use default look and feel
            
            new SwingTestApp().setVisible(true);
        });
    }
}
