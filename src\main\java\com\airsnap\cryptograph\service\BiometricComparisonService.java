package com.airsnap.cryptograph.service;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.BiometricData;
import com.airsnap.cryptograph.model.Cryptograph;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for comparing biometric data and cryptographs using AirSnap OmnimatchUtil
 */
public class BiometricComparisonService {
    private static final Logger logger = LoggerFactory.getLogger(BiometricComparisonService.class);
    
    private final AirSnapLibraryWrapper airSnapWrapper;
    private final DatabaseManager dbManager;
    private final ObjectMapper objectMapper;
    
    // Comparison thresholds
    private static final double FINGERPRINT_MATCH_THRESHOLD = 0.75;
    private static final double FACE_MATCH_THRESHOLD = 0.70;
    private static final double CRYPTOGRAPH_MATCH_THRESHOLD = 0.80;
    
    public BiometricComparisonService(DatabaseManager dbManager) {
        this.airSnapWrapper = new AirSnapLibraryWrapper();
        this.dbManager = dbManager;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Compare two fingerprint templates
     */
    public FingerprintComparisonResult compareFingerprintTemplates(byte[] template1, byte[] template2, 
                                                                  String sourceId, String targetId) {
        logger.info("Comparing fingerprint templates: {} vs {}", sourceId, targetId);
        
        try {
            // Validate input templates
            if (template1 == null || template1.length == 0) {
                return new FingerprintComparisonResult(false, 0.0, "Source template is empty", sourceId, targetId);
            }
            
            if (template2 == null || template2.length == 0) {
                return new FingerprintComparisonResult(false, 0.0, "Target template is empty", sourceId, targetId);
            }
            
            // Perform comparison using AirSnap OmniMatch
            double score = airSnapWrapper.safeCompareFingerprintTemplates(template1, template2);
            
            if (score < 0) {
                return new FingerprintComparisonResult(false, 0.0, "Comparison failed", sourceId, targetId);
            }
            
            boolean isMatch = score >= FINGERPRINT_MATCH_THRESHOLD;
            String message = isMatch ? "Fingerprints match" : "Fingerprints do not match";
            
            FingerprintComparisonResult result = new FingerprintComparisonResult(isMatch, score, message, sourceId, targetId);
            
            // Log comparison history
            logComparisonHistory("FINGERPRINT", sourceId, targetId, score, isMatch, "OMNIMATCH_FINGERPRINT", result.toString());
            
            logger.info("Fingerprint comparison completed: {} (Score: {})", message, score);
            return result;
            
        } catch (Exception e) {
            logger.error("Error comparing fingerprint templates", e);
            FingerprintComparisonResult errorResult = new FingerprintComparisonResult(
                false, 0.0, "Comparison error: " + e.getMessage(), sourceId, targetId);
            
            // Log failed comparison
            try {
                logComparisonHistory("FINGERPRINT", sourceId, targetId, 0.0, false, "OMNIMATCH_FINGERPRINT", e.getMessage());
            } catch (SQLException logError) {
                logger.error("Failed to log comparison history", logError);
            }
            
            return errorResult;
        }
    }
    
    /**
     * Compare two face templates
     */
    public FaceComparisonResult compareFaceTemplates(byte[] template1, byte[] template2, 
                                                    String sourceId, String targetId) {
        logger.info("Comparing face templates: {} vs {}", sourceId, targetId);
        
        try {
            // Validate input templates
            if (template1 == null || template1.length == 0) {
                return new FaceComparisonResult(false, 0.0, "Source template is empty", sourceId, targetId);
            }
            
            if (template2 == null || template2.length == 0) {
                return new FaceComparisonResult(false, 0.0, "Target template is empty", sourceId, targetId);
            }
            
            // Perform comparison using AirSnap OmniMatch
            double score = airSnapWrapper.compareFaceTemplates(template1, template2);
            
            if (score < 0) {
                return new FaceComparisonResult(false, 0.0, "Comparison failed", sourceId, targetId);
            }
            
            boolean isMatch = score >= FACE_MATCH_THRESHOLD;
            String message = isMatch ? "Faces match" : "Faces do not match";
            
            FaceComparisonResult result = new FaceComparisonResult(isMatch, score, message, sourceId, targetId);
            
            // Log comparison history
            logComparisonHistory("FACE", sourceId, targetId, score, isMatch, "OMNIMATCH_FACE", result.toString());
            
            logger.info("Face comparison completed: {} (Score: {})", message, score);
            return result;
            
        } catch (Exception e) {
            logger.error("Error comparing face templates", e);
            FaceComparisonResult errorResult = new FaceComparisonResult(
                false, 0.0, "Comparison error: " + e.getMessage(), sourceId, targetId);
            
            // Log failed comparison
            try {
                logComparisonHistory("FACE", sourceId, targetId, 0.0, false, "OMNIMATCH_FACE", e.getMessage());
            } catch (SQLException logError) {
                logger.error("Failed to log comparison history", logError);
            }
            
            return errorResult;
        }
    }
    
    /**
     * Compare two complete cryptographs
     */
    public CryptographComparisonResult compareCryptographs(byte[] cryptograph1, byte[] cryptograph2, 
                                                          String sourceId, String targetId) {
        logger.info("Comparing cryptographs: {} vs {}", sourceId, targetId);
        
        try {
            // Validate input cryptographs
            if (cryptograph1 == null || cryptograph1.length == 0) {
                return new CryptographComparisonResult(false, 0.0, "Source cryptograph is empty", sourceId, targetId, null);
            }
            
            if (cryptograph2 == null || cryptograph2.length == 0) {
                return new CryptographComparisonResult(false, 0.0, "Target cryptograph is empty", sourceId, targetId, null);
            }
            
            // Perform comparison using AirSnap library
            String comparisonResultJson = airSnapWrapper.compareCryptographs(cryptograph1, cryptograph2);
            
            if (comparisonResultJson == null || comparisonResultJson.isEmpty()) {
                return new CryptographComparisonResult(false, 0.0, "Comparison failed", sourceId, targetId, null);
            }
            
            // Parse comparison result
            JsonNode resultNode = objectMapper.readTree(comparisonResultJson);
            double overallScore = resultNode.has("overallScore") ? resultNode.get("overallScore").asDouble() : 0.0;
            
            boolean isMatch = overallScore >= CRYPTOGRAPH_MATCH_THRESHOLD;
            String message = isMatch ? "Cryptographs match" : "Cryptographs do not match";
            
            // Extract detailed comparison data
            CryptographComparisonDetails details = extractComparisonDetails(resultNode);
            
            CryptographComparisonResult result = new CryptographComparisonResult(
                isMatch, overallScore, message, sourceId, targetId, details);
            
            // Log comparison history
            logComparisonHistory("CRYPTOGRAPH", sourceId, targetId, overallScore, isMatch, 
                               "OMNIMATCH_CRYPTOGRAPH", comparisonResultJson);
            
            logger.info("Cryptograph comparison completed: {} (Score: {})", message, overallScore);
            return result;
            
        } catch (Exception e) {
            logger.error("Error comparing cryptographs", e);
            CryptographComparisonResult errorResult = new CryptographComparisonResult(
                false, 0.0, "Comparison error: " + e.getMessage(), sourceId, targetId, null);
            
            // Log failed comparison
            try {
                logComparisonHistory("CRYPTOGRAPH", sourceId, targetId, 0.0, false, "OMNIMATCH_CRYPTOGRAPH", e.getMessage());
            } catch (SQLException logError) {
                logger.error("Failed to log comparison history", logError);
            }
            
            return errorResult;
        }
    }
    
    /**
     * Compare biometric data from two different sources
     */
    public BiometricComparisonResult compareBiometricData(BiometricData biometric1, BiometricData biometric2) {
        logger.info("Comparing biometric data: ID {} vs ID {}", biometric1.getId(), biometric2.getId());
        
        List<ComparisonResult> results = new ArrayList<>();
        
        // Compare fingerprints if both have fingerprint data
        if (biometric1.hasFingerprintData() && biometric2.hasFingerprintData()) {
            FingerprintComparisonResult fingerprintResult = compareFingerprintTemplates(
                biometric1.getFingerprintTemplate(), 
                biometric2.getFingerprintTemplate(),
                biometric1.getId().toString(),
                biometric2.getId().toString()
            );
            results.add(fingerprintResult);
        }
        
        // Compare faces if both have face data
        if (biometric1.hasFaceData() && biometric2.hasFaceData()) {
            FaceComparisonResult faceResult = compareFaceTemplates(
                biometric1.getFaceTemplate(),
                biometric2.getFaceTemplate(),
                biometric1.getId().toString(),
                biometric2.getId().toString()
            );
            results.add(faceResult);
        }
        
        // Calculate overall result
        if (results.isEmpty()) {
            return new BiometricComparisonResult(false, 0.0, "No comparable biometric data found", results);
        }
        
        double averageScore = results.stream().mapToDouble(ComparisonResult::getScore).average().orElse(0.0);
        boolean overallMatch = results.stream().anyMatch(ComparisonResult::isMatch);
        String message = overallMatch ? "Biometric data matches" : "Biometric data does not match";
        
        return new BiometricComparisonResult(overallMatch, averageScore, message, results);
    }
    
    /**
     * Get comparison history for a specific entity
     */
    public List<ComparisonHistoryRecord> getComparisonHistory(String entityId) throws SQLException {
        String sql = """
            SELECT * FROM comparison_history 
            WHERE source_id = ? OR target_id = ?
            ORDER BY compared_at DESC
        """;
        
        List<ComparisonHistoryRecord> history = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, entityId);
            stmt.setString(2, entityId);
            
            var rs = stmt.executeQuery();
            while (rs.next()) {
                history.add(new ComparisonHistoryRecord(
                    rs.getLong("id"),
                    rs.getString("comparison_type"),
                    rs.getString("source_id"),
                    rs.getString("target_id"),
                    rs.getDouble("comparison_score"),
                    rs.getBoolean("match_result"),
                    rs.getString("comparison_method"),
                    rs.getString("comparison_details"),
                    rs.getTimestamp("compared_at").toLocalDateTime()
                ));
            }
        }
        
        return history;
    }
    
    // Helper methods
    
    private void logComparisonHistory(String comparisonType, String sourceId, String targetId, 
                                    double score, boolean match, String method, String details) throws SQLException {
        String sql = """
            INSERT INTO comparison_history (comparison_type, source_id, target_id, comparison_score, 
                                          match_result, comparison_method, comparison_details)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, comparisonType);
            stmt.setString(2, sourceId);
            stmt.setString(3, targetId);
            stmt.setDouble(4, score);
            stmt.setBoolean(5, match);
            stmt.setString(6, method);
            stmt.setString(7, details);
            
            stmt.executeUpdate();
            
            // Log audit
            dbManager.logAudit("COMPARE", comparisonType, sourceId + "_vs_" + targetId, 
                             "SYSTEM", "Biometric comparison performed", true, null);
        }
    }
    
    private CryptographComparisonDetails extractComparisonDetails(JsonNode resultNode) {
        try {
            double demographicScore = resultNode.has("demographicScore") ? resultNode.get("demographicScore").asDouble() : 0.0;
            double faceScore = resultNode.has("faceScore") ? resultNode.get("faceScore").asDouble() : 0.0;
            double fingerprintScore = resultNode.has("fingerprintScore") ? resultNode.get("fingerprintScore").asDouble() : 0.0;
            
            return new CryptographComparisonDetails(demographicScore, faceScore, fingerprintScore);
        } catch (Exception e) {
            logger.warn("Failed to extract comparison details", e);
            return new CryptographComparisonDetails(0.0, 0.0, 0.0);
        }
    }
    
    // Inner classes for results
    
    public abstract static class ComparisonResult {
        protected final boolean match;
        protected final double score;
        protected final String message;
        protected final LocalDateTime comparisonTime;
        
        public ComparisonResult(boolean match, double score, String message) {
            this.match = match;
            this.score = score;
            this.message = message;
            this.comparisonTime = LocalDateTime.now();
        }
        
        public boolean isMatch() { return match; }
        public double getScore() { return score; }
        public String getMessage() { return message; }
        public LocalDateTime getComparisonTime() { return comparisonTime; }
    }
    
    public static class FingerprintComparisonResult extends ComparisonResult {
        private final String sourceId;
        private final String targetId;
        
        public FingerprintComparisonResult(boolean match, double score, String message, String sourceId, String targetId) {
            super(match, score, message);
            this.sourceId = sourceId;
            this.targetId = targetId;
        }
        
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }
        
        @Override
        public String toString() {
            return String.format("FingerprintComparison{match=%s, score=%.3f, source=%s, target=%s}", 
                               match, score, sourceId, targetId);
        }
    }
    
    public static class FaceComparisonResult extends ComparisonResult {
        private final String sourceId;
        private final String targetId;
        
        public FaceComparisonResult(boolean match, double score, String message, String sourceId, String targetId) {
            super(match, score, message);
            this.sourceId = sourceId;
            this.targetId = targetId;
        }
        
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }
        
        @Override
        public String toString() {
            return String.format("FaceComparison{match=%s, score=%.3f, source=%s, target=%s}", 
                               match, score, sourceId, targetId);
        }
    }
    
    public static class CryptographComparisonResult extends ComparisonResult {
        private final String sourceId;
        private final String targetId;
        private final CryptographComparisonDetails details;
        
        public CryptographComparisonResult(boolean match, double score, String message, 
                                         String sourceId, String targetId, CryptographComparisonDetails details) {
            super(match, score, message);
            this.sourceId = sourceId;
            this.targetId = targetId;
            this.details = details;
        }
        
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }
        public CryptographComparisonDetails getDetails() { return details; }
        
        @Override
        public String toString() {
            return String.format("CryptographComparison{match=%s, score=%.3f, source=%s, target=%s}", 
                               match, score, sourceId, targetId);
        }
    }
    
    public static class BiometricComparisonResult extends ComparisonResult {
        private final List<ComparisonResult> individualResults;
        
        public BiometricComparisonResult(boolean match, double score, String message, List<ComparisonResult> individualResults) {
            super(match, score, message);
            this.individualResults = individualResults;
        }
        
        public List<ComparisonResult> getIndividualResults() { return individualResults; }
    }
    
    public static class CryptographComparisonDetails {
        private final double demographicScore;
        private final double faceScore;
        private final double fingerprintScore;
        
        public CryptographComparisonDetails(double demographicScore, double faceScore, double fingerprintScore) {
            this.demographicScore = demographicScore;
            this.faceScore = faceScore;
            this.fingerprintScore = fingerprintScore;
        }
        
        public double getDemographicScore() { return demographicScore; }
        public double getFaceScore() { return faceScore; }
        public double getFingerprintScore() { return fingerprintScore; }
    }
    
    public static class ComparisonHistoryRecord {
        private final Long id;
        private final String comparisonType;
        private final String sourceId;
        private final String targetId;
        private final double comparisonScore;
        private final boolean matchResult;
        private final String comparisonMethod;
        private final String comparisonDetails;
        private final LocalDateTime comparedAt;
        
        public ComparisonHistoryRecord(Long id, String comparisonType, String sourceId, String targetId, 
                                     double comparisonScore, boolean matchResult, String comparisonMethod, 
                                     String comparisonDetails, LocalDateTime comparedAt) {
            this.id = id;
            this.comparisonType = comparisonType;
            this.sourceId = sourceId;
            this.targetId = targetId;
            this.comparisonScore = comparisonScore;
            this.matchResult = matchResult;
            this.comparisonMethod = comparisonMethod;
            this.comparisonDetails = comparisonDetails;
            this.comparedAt = comparedAt;
        }
        
        // Getters
        public Long getId() { return id; }
        public String getComparisonType() { return comparisonType; }
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }
        public double getComparisonScore() { return comparisonScore; }
        public boolean isMatchResult() { return matchResult; }
        public String getComparisonMethod() { return comparisonMethod; }
        public String getComparisonDetails() { return comparisonDetails; }
        public LocalDateTime getComparedAt() { return comparedAt; }
    }
}
