package com.airsnap.integration;

import com.airsnap.integration.impl.ComputerVisionServiceImpl;
import com.airsnap.integration.interfaces.ComputerVisionService;
import com.airsnap.integration.interfaces.ComputerVisionService.*;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;

/**
 * Simple test runner to verify computer vision functionality
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("=== AirSnap Computer Vision Test Runner ===\n");
        
        // Create test images
        createTestImages();
        
        // Test computer vision service
        testComputerVisionService();
        
        System.out.println("\n=== Test Runner Complete ===");
        System.out.println("You can now run the JavaFX application with:");
        System.out.println("java -cp target/classes com.airsnap.integration.AirSnapDesktopApp");
    }
    
    private static void createTestImages() {
        System.out.println("1. Creating test images...");
        
        try {
            File testDir = new File("test-images");
            testDir.mkdirs();
            
            // Create a simple test image
            BufferedImage testImage = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = testImage.createGraphics();
            
            // Background
            g2d.setColor(Color.LIGHT_GRAY);
            g2d.fillRect(0, 0, 640, 480);
            
            // Draw some shapes
            g2d.setColor(Color.RED);
            g2d.fillOval(50, 50, 100, 100);
            
            g2d.setColor(Color.BLUE);
            g2d.fillRect(200, 50, 100, 100);
            
            g2d.setColor(Color.GREEN);
            int[] xPoints = {350, 400, 450};
            int[] yPoints = {150, 50, 150};
            g2d.fillPolygon(xPoints, yPoints, 3);
            
            // Add text
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("Arial", Font.BOLD, 24));
            g2d.drawString("AirSnap Test", 50, 250);
            
            g2d.dispose();
            
            // Save the image
            ImageIO.write(testImage, "png", new File("test-images/test-image.png"));
            
            System.out.println("   ✓ Test image created: test-images/test-image.png");
            
        } catch (IOException e) {
            System.err.println("   ✗ Error creating test images: " + e.getMessage());
        }
    }
    
    private static void testComputerVisionService() {
        System.out.println("\n2. Testing Computer Vision Service...");
        
        ComputerVisionService cvService = new ComputerVisionServiceImpl();
        
        try {
            // Test initialization
            System.out.print("   Testing initialization... ");
            boolean initialized = cvService.initialize();
            System.out.println(initialized ? "✓ PASS" : "✗ FAIL");
            
            if (!initialized) {
                System.out.println("   Cannot continue tests without initialization");
                return;
            }
            
            // Create test image
            BufferedImage testImage = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = testImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, 640, 480);
            g2d.setColor(Color.BLACK);
            g2d.fillOval(100, 100, 200, 200);
            g2d.dispose();
            
            // Test object detection
            System.out.print("   Testing object detection... ");
            try {
                List<ObjectDetectionResult> objects = cvService.detectObjects(testImage);
                System.out.println("✓ PASS (detected " + objects.size() + " objects)");
                for (ObjectDetectionResult obj : objects) {
                    System.out.println("     - " + obj.getClassName() + " (" + 
                                     String.format("%.1f%%", obj.getConfidence() * 100) + ")");
                }
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test feature extraction
            System.out.print("   Testing feature extraction (SIFT)... ");
            try {
                float[] features = cvService.extractImageFeatures(testImage, FeatureType.SIFT);
                System.out.println("✓ PASS (extracted " + features.length + " features)");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test image enhancement
            System.out.print("   Testing image enhancement... ");
            try {
                BufferedImage enhanced = cvService.enhanceImageQuality(testImage, EnhancementType.BRIGHTNESS_ADJUSTMENT);
                System.out.println("✓ PASS (enhanced image: " + enhanced.getWidth() + "x" + enhanced.getHeight() + ")");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test image segmentation
            System.out.print("   Testing image segmentation... ");
            try {
                BufferedImage segmented = cvService.performImageSegmentation(testImage, SegmentationType.SEMANTIC);
                System.out.println("✓ PASS (segmented image: " + segmented.getWidth() + "x" + segmented.getHeight() + ")");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test OCR
            System.out.print("   Testing OCR... ");
            try {
                String text = cvService.performOCR(testImage);
                System.out.println("✓ PASS (detected text: \"" + text + "\")");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test neural network processing
            System.out.print("   Testing neural network processing... ");
            try {
                cvService.loadNeuralNetworkModel("/test/model.onnx", ModelType.CLASSIFICATION);
                NeuralNetworkResult result = cvService.processWithNeuralNetwork(testImage, "model.onnx");
                System.out.println("✓ PASS (processing time: " + result.getProcessingTime() + "ms)");
                System.out.println("     Predictions:");
                for (Map.Entry<String, Float> entry : result.getPredictions().entrySet()) {
                    System.out.println("       " + entry.getKey() + ": " + 
                                     String.format("%.1f%%", entry.getValue() * 100));
                }
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test image filters
            System.out.print("   Testing image filters... ");
            try {
                List<ImageFilter> filters = new ArrayList<>();
                Map<String, Object> params = new HashMap<>();
                params.put("radius", 3);
                filters.add(new ImageFilter("Gaussian Blur", params));
                
                BufferedImage filtered = cvService.applyImageFilters(testImage, filters);
                System.out.println("✓ PASS (applied " + filters.size() + " filters)");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test image similarity
            System.out.print("   Testing image similarity... ");
            try {
                BufferedImage image2 = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
                double similarity = cvService.calculateImageSimilarity(testImage, image2, SimilarityMetric.COSINE);
                System.out.println("✓ PASS (similarity: " + String.format("%.3f", similarity) + ")");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test statistics
            System.out.print("   Testing processing statistics... ");
            try {
                ProcessingStatistics stats = cvService.getProcessingStatistics();
                System.out.println("✓ PASS");
                System.out.println("     Total processed images: " + stats.getTotalProcessedImages());
                System.out.println("     Average processing time: " + String.format("%.2f ms", stats.getAverageProcessingTime()));
                System.out.println("     Operations performed: " + stats.getOperationCounts().size());
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
            // Test cleanup
            System.out.print("   Testing cleanup... ");
            try {
                cvService.cleanup();
                System.out.println("✓ PASS");
            } catch (Exception e) {
                System.out.println("✗ FAIL: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("   ✗ Unexpected error during testing: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
