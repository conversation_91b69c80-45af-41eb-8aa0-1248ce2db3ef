package com.airsnap.cryptograph.model;

import java.time.LocalDateTime;

/**
 * Model class representing a generated cryptograph
 */
public class Cryptograph {
    private Long id;
    private String cryptographId;
    private Long demographicId;
    private Long biometricId;
    private byte[] cryptographData;
    private String cryptographHash;
    private String generationMethod;
    private LocalDateTime createdAt;
    
    // Associated objects
    private DemographicInfo demographicInfo;
    private BiometricData biometricData;
    
    // Constructors
    public Cryptograph() {}
    
    public Cryptograph(String cryptographId, Long demographicId, Long biometricId, 
                      byte[] cryptographData, String cryptographHash, String generationMethod) {
        this.cryptographId = cryptographId;
        this.demographicId = demographicId;
        this.biometricId = biometricId;
        this.cryptographData = cryptographData;
        this.cryptographHash = cryptographHash;
        this.generationMethod = generationMethod;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getCryptographId() { return cryptographId; }
    public void setCryptographId(String cryptographId) { this.cryptographId = cryptographId; }
    
    public Long getDemographicId() { return demographicId; }
    public void setDemographicId(Long demographicId) { this.demographicId = demographicId; }
    
    public Long getBiometricId() { return biometricId; }
    public void setBiometricId(Long biometricId) { this.biometricId = biometricId; }
    
    public byte[] getCryptographData() { return cryptographData; }
    public void setCryptographData(byte[] cryptographData) { this.cryptographData = cryptographData; }
    
    public String getCryptographHash() { return cryptographHash; }
    public void setCryptographHash(String cryptographHash) { this.cryptographHash = cryptographHash; }
    
    public String getGenerationMethod() { return generationMethod; }
    public void setGenerationMethod(String generationMethod) { this.generationMethod = generationMethod; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public DemographicInfo getDemographicInfo() { return demographicInfo; }
    public void setDemographicInfo(DemographicInfo demographicInfo) { this.demographicInfo = demographicInfo; }
    
    public BiometricData getBiometricData() { return biometricData; }
    public void setBiometricData(BiometricData biometricData) { this.biometricData = biometricData; }
    
    public int getDataSize() {
        return cryptographData != null ? cryptographData.length : 0;
    }
    
    @Override
    public String toString() {
        return "Cryptograph{" +
                "id=" + id +
                ", cryptographId='" + cryptographId + '\'' +
                ", demographicId=" + demographicId +
                ", biometricId=" + biometricId +
                ", dataSize=" + getDataSize() +
                ", cryptographHash='" + cryptographHash + '\'' +
                ", generationMethod='" + generationMethod + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
