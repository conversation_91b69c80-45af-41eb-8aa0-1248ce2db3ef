package com.airsnap.integration;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import javax.imageio.ImageIO;

/**
 * Utility class to generate test images for testing computer vision functions
 */
public class TestImageGenerator {
    
    /**
     * Creates a test image with various shapes and patterns
     */
    public static BufferedImage createTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Enable antialiasing for better quality
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // Fill background with gradient
        GradientPaint gradient = new GradientPaint(0, 0, Color.LIGHT_GRAY, width, height, Color.WHITE);
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);
        
        // Draw some geometric shapes
        g2d.setColor(Color.RED);
        g2d.fillOval(50, 50, 100, 100);
        
        g2d.setColor(Color.BLUE);
        g2d.fillRect(200, 50, 100, 100);
        
        g2d.setColor(Color.GREEN);
        int[] xPoints = {350, 400, 450};
        int[] yPoints = {150, 50, 150};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        // Draw some text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.BOLD, 24));
        g2d.drawString("AirSnap Test Image", 50, 250);
        
        g2d.setFont(new Font("Arial", Font.PLAIN, 16));
        g2d.drawString("Computer Vision Testing", 50, 280);
        g2d.drawString("Object Detection Sample", 50, 300);
        
        // Draw some lines and patterns
        g2d.setColor(Color.MAGENTA);
        g2d.setStroke(new BasicStroke(3));
        for (int i = 0; i < 10; i++) {
            g2d.drawLine(50 + i * 20, 350, 50 + i * 20, 400);
        }
        
        // Draw circles pattern
        g2d.setColor(Color.CYAN);
        for (int i = 0; i < 5; i++) {
            g2d.drawOval(300 + i * 30, 300, 20, 20);
        }
        
        g2d.dispose();
        return image;
    }
    
    /**
     * Creates a simple face-like pattern for face detection testing
     */
    public static BufferedImage createFaceTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // Background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Face outline
        g2d.setColor(Color.PINK);
        g2d.fillOval(width/4, height/4, width/2, height/2);
        
        // Eyes
        g2d.setColor(Color.BLACK);
        g2d.fillOval(width/3, height/3, 20, 15);
        g2d.fillOval(2*width/3 - 20, height/3, 20, 15);
        
        // Nose
        g2d.drawLine(width/2, height/2 - 10, width/2, height/2 + 10);
        
        // Mouth
        g2d.drawArc(width/2 - 30, height/2 + 10, 60, 30, 0, -180);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * Creates an image with text for OCR testing
     */
    public static BufferedImage createTextTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 32));
        g2d.drawString("AirSnap Biometric", 50, 100);
        
        g2d.setFont(new Font("Arial", Font.PLAIN, 24));
        g2d.drawString("Identity Verification", 50, 150);
        g2d.drawString("Secure Access Control", 50, 200);
        
        g2d.setFont(new Font("Courier", Font.BOLD, 18));
        g2d.drawString("System Ready", 50, 250);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * Saves a BufferedImage to a file
     */
    public static void saveImage(BufferedImage image, String filename) throws IOException {
        File outputFile = new File(filename);
        outputFile.getParentFile().mkdirs();
        ImageIO.write(image, "png", outputFile);
    }
    
    /**
     * Main method to generate sample test images
     */
    public static void main(String[] args) {
        try {
            // Create test images directory
            File testDir = new File("test-images");
            testDir.mkdirs();
            
            // Generate various test images
            BufferedImage testImage = createTestImage(640, 480);
            saveImage(testImage, "test-images/test-image.png");
            
            BufferedImage faceImage = createFaceTestImage(400, 400);
            saveImage(faceImage, "test-images/face-test.png");
            
            BufferedImage textImage = createTextTestImage(500, 300);
            saveImage(textImage, "test-images/text-test.png");
            
            System.out.println("Test images generated successfully!");
            System.out.println("- test-images/test-image.png");
            System.out.println("- test-images/face-test.png");
            System.out.println("- test-images/text-test.png");
            
        } catch (IOException e) {
            System.err.println("Error generating test images: " + e.getMessage());
        }
    }
}
