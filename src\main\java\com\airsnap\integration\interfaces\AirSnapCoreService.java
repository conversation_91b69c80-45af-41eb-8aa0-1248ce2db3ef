package com.airsnap.integration.interfaces;

import java.util.List;
import java.util.Map;

/**
 * Interface for Core AirSnap functionality
 * Represents T5AirSnap library
 */
public interface AirSnapCoreService {
    
    /**
     * Initialize the AirSnap core service
     * @param config Configuration parameters
     * @return true if initialization successful
     */
    boolean initialize(Map<String, Object> config);
    
    /**
     * Start biometric session
     * @param sessionType Type of biometric session
     * @return Session identifier
     */
    String startBiometricSession(BiometricSessionType sessionType);
    
    /**
     * End biometric session
     * @param sessionId Session identifier
     * @return Session result
     */
    BiometricSessionResult endBiometricSession(String sessionId);
    
    /**
     * Process biometric data
     * @param sessionId Session identifier
     * @param biometricData Raw biometric data
     * @return Processing result
     */
    BiometricProcessingResult processBiometricData(String sessionId, byte[] biometricData);
    
    /**
     * Get device information
     * @return Device information
     */
    DeviceInfo getDeviceInfo();
    
    /**
     * Perform system health check
     * @return Health check result
     */
    SystemHealthResult performHealthCheck();
    
    /**
     * Get supported biometric modalities
     * @return List of supported modalities
     */
    List<BiometricModality> getSupportedModalities();
    
    /**
     * Configure biometric parameters
     * @param modality Biometric modality
     * @param parameters Configuration parameters
     * @return true if configuration successful
     */
    boolean configureBiometricParameters(BiometricModality modality, Map<String, Object> parameters);
    
    /**
     * Get system logs
     * @param level Log level filter
     * @param limit Maximum number of logs
     * @return System logs
     */
    List<SystemLog> getSystemLogs(LogLevel level, int limit);
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Enums and inner classes
    public enum BiometricSessionType {
        FACE_RECOGNITION,
        FINGERPRINT_RECOGNITION,
        MULTI_MODAL,
        ENROLLMENT,
        VERIFICATION
    }
    
    public enum BiometricModality {
        FACE,
        FINGERPRINT,
        IRIS,
        VOICE,
        PALM_PRINT
    }
    
    public enum LogLevel {
        DEBUG,
        INFO,
        WARN,
        ERROR,
        FATAL
    }
    
    public static class BiometricSessionResult {
        private final String sessionId;
        private final boolean success;
        private final String message;
        private final Map<String, Object> results;
        
        public BiometricSessionResult(String sessionId, boolean success, String message, Map<String, Object> results) {
            this.sessionId = sessionId;
            this.success = success;
            this.message = message;
            this.results = results;
        }
        
        // Getters
        public String getSessionId() { return sessionId; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Map<String, Object> getResults() { return results; }
    }
    
    public static class BiometricProcessingResult {
        private final boolean processed;
        private final double quality;
        private final byte[] processedData;
        private final String message;
        
        public BiometricProcessingResult(boolean processed, double quality, byte[] processedData, String message) {
            this.processed = processed;
            this.quality = quality;
            this.processedData = processedData;
            this.message = message;
        }
        
        // Getters
        public boolean isProcessed() { return processed; }
        public double getQuality() { return quality; }
        public byte[] getProcessedData() { return processedData; }
        public String getMessage() { return message; }
    }
    
    public static class DeviceInfo {
        private final String deviceId;
        private final String model;
        private final String version;
        private final Map<String, String> capabilities;
        
        public DeviceInfo(String deviceId, String model, String version, Map<String, String> capabilities) {
            this.deviceId = deviceId;
            this.model = model;
            this.version = version;
            this.capabilities = capabilities;
        }
        
        // Getters
        public String getDeviceId() { return deviceId; }
        public String getModel() { return model; }
        public String getVersion() { return version; }
        public Map<String, String> getCapabilities() { return capabilities; }
    }
    
    public static class SystemHealthResult {
        private final boolean healthy;
        private final Map<String, String> componentStatus;
        private final List<String> issues;
        
        public SystemHealthResult(boolean healthy, Map<String, String> componentStatus, List<String> issues) {
            this.healthy = healthy;
            this.componentStatus = componentStatus;
            this.issues = issues;
        }
        
        // Getters
        public boolean isHealthy() { return healthy; }
        public Map<String, String> getComponentStatus() { return componentStatus; }
        public List<String> getIssues() { return issues; }
    }
    
    public static class SystemLog {
        private final long timestamp;
        private final LogLevel level;
        private final String message;
        private final String component;
        
        public SystemLog(long timestamp, LogLevel level, String message, String component) {
            this.timestamp = timestamp;
            this.level = level;
            this.message = message;
            this.component = component;
        }
        
        // Getters
        public long getTimestamp() { return timestamp; }
        public LogLevel getLevel() { return level; }
        public String getMessage() { return message; }
        public String getComponent() { return component; }
    }
}
