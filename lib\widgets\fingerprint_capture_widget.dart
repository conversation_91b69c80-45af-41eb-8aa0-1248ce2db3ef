import 'dart:io';
import 'package:flutter/material.dart';
import 'camera_capture_widget.dart';

enum FingerType {
  rightThumb('Right Thumb'),
  rightIndex('Right Index'),
  rightMiddle('Right Middle'),
  rightRing('Right Ring'),
  rightPinky('Right Pinky'),
  leftThumb('Left Thumb'),
  leftIndex('Left Index'),
  leftMiddle('Left Middle'),
  leftRing('Left Ring'),
  leftPinky('Left Pinky');

  const FingerType(this.displayName);
  final String displayName;
}

class FingerprintCaptureWidget extends StatefulWidget {
  final Function(File, FingerType) onFingerprintCaptured;
  final FingerType? preselectedFinger;
  final String? instructions;

  const FingerprintCaptureWidget({
    super.key,
    required this.onFingerprintCaptured,
    this.preselectedFinger,
    this.instructions,
  });

  @override
  State<FingerprintCaptureWidget> createState() => _FingerprintCaptureWidgetState();
}

class _FingerprintCaptureWidgetState extends State<FingerprintCaptureWidget> {
  FingerType _selectedFinger = FingerType.rightIndex;
  bool _showFingerSelection = true;

  @override
  void initState() {
    super.initState();
    if (widget.preselectedFinger != null) {
      _selectedFinger = widget.preselectedFinger!;
      _showFingerSelection = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_showFingerSelection) {
      return _buildFingerSelectionScreen();
    }

    return CameraCaptureWidget(
      title: 'Capture ${_selectedFinger.displayName}',
      subtitle: widget.instructions ?? 
          'Place your ${_selectedFinger.displayName.toLowerCase()} on a flat surface\n'
          'Ensure the fingerprint is clearly visible and well-lit',
      onImageCaptured: (File fingerprintFile) {
        widget.onFingerprintCaptured(fingerprintFile, _selectedFinger);
      },
      preferredLens: CameraLensDirection.back,
      showGalleryOption: true,
      overlayWidget: FingerprintOverlayWidget(fingerType: _selectedFinger),
    );
  }

  Widget _buildFingerSelectionScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Finger'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.fingerprint,
                          color: Colors.blue[600],
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Fingerprint Capture',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Select which finger you want to capture for the biometric cryptograph. '
                      'Ensure the selected finger is clean and dry for best results.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            const Text(
              'Right Hand',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            _buildFingerGrid([
              FingerType.rightThumb,
              FingerType.rightIndex,
              FingerType.rightMiddle,
              FingerType.rightRing,
              FingerType.rightPinky,
            ]),

            const SizedBox(height: 24),

            const Text(
              'Left Hand',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            _buildFingerGrid([
              FingerType.leftThumb,
              FingerType.leftIndex,
              FingerType.leftMiddle,
              FingerType.leftRing,
              FingerType.leftPinky,
            ]),

            const Spacer(),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showFingerSelection = false;
                  });
                },
                icon: const Icon(Icons.camera_alt),
                label: Text('Capture ${_selectedFinger.displayName}'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFingerGrid(List<FingerType> fingers) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: fingers.length,
      itemBuilder: (context, index) {
        final finger = fingers[index];
        final isSelected = finger == _selectedFinger;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFinger = finger;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue[100] : Colors.grey[100],
              border: Border.all(
                color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.fingerprint,
                    color: isSelected ? Colors.blue[600] : Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      finger.displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.blue[600] : Colors.grey[600],
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class FingerprintOverlayWidget extends StatelessWidget {
  final FingerType fingerType;

  const FingerprintOverlayWidget({
    super.key,
    required this.fingerType,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Dark overlay with circular cutout for fingerprint
        Container(
          color: Colors.black.withOpacity(0.5),
          child: Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 3,
                ),
              ),
            ),
          ),
        ),

        // Fingerprint guide overlay
        Center(
          child: CustomPaint(
            size: const Size(200, 200),
            painter: FingerprintGuidePainter(),
          ),
        ),

        // Instructions overlay
        Positioned(
          bottom: 150,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.fingerprint,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  '${fingerType.displayName} Capture',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Fingerprint Guidelines:\n'
                  '• Place finger flat on surface\n'
                  '• Ensure good lighting\n'
                  '• Keep finger steady\n'
                  '• Clean finger if needed\n'
                  '• Center in the circle',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class FingerprintGuidePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = size.width / 2 - 10;

    // Draw concentric circles to guide fingerprint placement
    for (int i = 1; i <= 3; i++) {
      canvas.drawCircle(
        Offset(centerX, centerY),
        radius * (i / 3),
        paint,
      );
    }

    // Draw crosshairs
    canvas.drawLine(
      Offset(centerX - 20, centerY),
      Offset(centerX + 20, centerY),
      paint,
    );
    canvas.drawLine(
      Offset(centerX, centerY - 20),
      Offset(centerX, centerY + 20),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class FingerprintCaptureDialog extends StatefulWidget {
  final Function(File, FingerType) onFingerprintCaptured;
  final FingerType? preselectedFinger;

  const FingerprintCaptureDialog({
    super.key,
    required this.onFingerprintCaptured,
    this.preselectedFinger,
  });

  @override
  State<FingerprintCaptureDialog> createState() => _FingerprintCaptureDialogState();
}

class _FingerprintCaptureDialogState extends State<FingerprintCaptureDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: FingerprintCaptureWidget(
        onFingerprintCaptured: (File fingerprintFile, FingerType fingerType) {
          widget.onFingerprintCaptured(fingerprintFile, fingerType);
          Navigator.of(context).pop({'file': fingerprintFile, 'type': fingerType});
        },
        preselectedFinger: widget.preselectedFinger,
      ),
    );
  }
}

// Fingerprint capture result widget
class FingerprintCaptureResult extends StatelessWidget {
  final File? fingerprintImage;
  final FingerType? fingerType;
  final VoidCallback? onRetake;
  final VoidCallback? onConfirm;

  const FingerprintCaptureResult({
    super.key,
    this.fingerprintImage,
    this.fingerType,
    this.onRetake,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.fingerprint,
                  color: Colors.green[600],
                ),
                const SizedBox(width: 8),
                Text(
                  fingerType?.displayName ?? 'Fingerprint',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (fingerprintImage != null) ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    fingerprintImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onRetake,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retake'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onConfirm,
                      icon: const Icon(Icons.check),
                      label: const Text('Confirm'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.fingerprint,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'No fingerprint captured',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onRetake,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Capture Fingerprint'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
