package com.airsnap.integration.impl;

import com.airsnap.integration.interfaces.AirSnapCoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Implementation of AirSnap Core Service
 * Simulates T5AirSnap main library functionality
 */
public class AirSnapCoreServiceImpl implements AirSnapCoreService {
    
    private static final Logger logger = LoggerFactory.getLogger(AirSnapCoreServiceImpl.class);
    private final Random random = new Random();
    private final AtomicLong sessionCounter = new AtomicLong(0);
    private final Map<String, BiometricSession> activeSessions = new ConcurrentHashMap<>();
    private final List<SystemLog> systemLogs = new ArrayList<>();
    private boolean initialized = false;
    
    @Override
    public boolean initialize(Map<String, Object> config) {
        logger.info("Initializing AirSnap Core Service with config: {}", config);
        try {
            Thread.sleep(1500);
            initialized = true;
            addSystemLog(LogLevel.INFO, "AirSnap Core Service initialized", "CORE");
            return true;
        } catch (InterruptedException e) {
            logger.error("Failed to initialize AirSnap Core Service", e);
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    @Override
    public String startBiometricSession(BiometricSessionType sessionType) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        String sessionId = "SESSION_" + sessionCounter.incrementAndGet();
        BiometricSession session = new BiometricSession(sessionId, sessionType, System.currentTimeMillis());
        activeSessions.put(sessionId, session);
        
        logger.debug("Started biometric session: {} (type: {})", sessionId, sessionType);
        addSystemLog(LogLevel.INFO, "Biometric session started: " + sessionId, "SESSION");
        return sessionId;
    }
    
    @Override
    public BiometricSessionResult endBiometricSession(String sessionId) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        BiometricSession session = activeSessions.remove(sessionId);
        if (session == null) {
            return new BiometricSessionResult(sessionId, false, "Session not found", Collections.emptyMap());
        }
        
        boolean success = random.nextDouble() > 0.1; // 90% success rate
        String message = success ? "Session completed successfully" : "Session failed";
        
        Map<String, Object> results = new HashMap<>();
        results.put("duration", System.currentTimeMillis() - session.startTime);
        results.put("session_type", session.sessionType);
        results.put("processed_data_count", random.nextInt(10) + 1);
        
        logger.debug("Ended biometric session: {} (success: {})", sessionId, success);
        addSystemLog(LogLevel.INFO, "Biometric session ended: " + sessionId, "SESSION");
        return new BiometricSessionResult(sessionId, success, message, results);
    }
    
    @Override
    public BiometricProcessingResult processBiometricData(String sessionId, byte[] biometricData) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        BiometricSession session = activeSessions.get(sessionId);
        if (session == null) {
            return new BiometricProcessingResult(false, 0.0, null, "Session not found");
        }
        
        boolean processed = random.nextDouble() > 0.05; // 95% success rate
        double quality = 0.6 + (random.nextDouble() * 0.4); // Quality 0.6-1.0
        
        byte[] processedData = null;
        if (processed) {
            processedData = new byte[biometricData.length / 2]; // Simulate compression
            random.nextBytes(processedData);
        }
        
        String message = processed ? "Data processed successfully" : "Data processing failed";
        
        logger.debug("Processed biometric data for session: {} (quality: {})", sessionId, quality);
        return new BiometricProcessingResult(processed, quality, processedData, message);
    }
    
    @Override
    public DeviceInfo getDeviceInfo() {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("face_recognition", "enabled");
        capabilities.put("fingerprint_recognition", "enabled");
        capabilities.put("encryption", "AES-256");
        capabilities.put("secure_storage", "available");
        
        return new DeviceInfo("DEVICE_001", "AirSnap Desktop Simulator", "1.0.0", capabilities);
    }
    
    @Override
    public SystemHealthResult performHealthCheck() {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        Map<String, String> componentStatus = new HashMap<>();
        componentStatus.put("face_service", "healthy");
        componentStatus.put("fingerprint_service", "healthy");
        componentStatus.put("crypto_service", "healthy");
        componentStatus.put("storage", "healthy");
        componentStatus.put("network", random.nextBoolean() ? "healthy" : "warning");
        
        List<String> issues = new ArrayList<>();
        if ("warning".equals(componentStatus.get("network"))) {
            issues.add("Network connectivity intermittent");
        }
        
        boolean healthy = issues.isEmpty();
        
        logger.debug("System health check completed: {} issues found", issues.size());
        addSystemLog(LogLevel.INFO, "System health check performed", "HEALTH");
        return new SystemHealthResult(healthy, componentStatus, issues);
    }
    
    @Override
    public List<BiometricModality> getSupportedModalities() {
        return Arrays.asList(BiometricModality.FACE, BiometricModality.FINGERPRINT);
    }
    
    @Override
    public boolean configureBiometricParameters(BiometricModality modality, Map<String, Object> parameters) {
        if (!initialized) {
            throw new IllegalStateException("Service not initialized");
        }
        
        logger.debug("Configuring {} parameters: {}", modality, parameters);
        addSystemLog(LogLevel.INFO, "Biometric parameters configured for " + modality, "CONFIG");
        return true;
    }
    
    @Override
    public List<SystemLog> getSystemLogs(LogLevel level, int limit) {
        return systemLogs.stream()
                .filter(log -> log.getLevel().ordinal() >= level.ordinal())
                .sorted((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()))
                .limit(limit)
                .toList();
    }
    
    @Override
    public void cleanup() {
        logger.info("Cleaning up AirSnap Core Service");
        activeSessions.clear();
        systemLogs.clear();
        initialized = false;
    }
    
    private void addSystemLog(LogLevel level, String message, String component) {
        systemLogs.add(new SystemLog(System.currentTimeMillis(), level, message, component));
        if (systemLogs.size() > 1000) {
            systemLogs.remove(0); // Keep only last 1000 logs
        }
    }
    
    private static class BiometricSession {
        final String sessionId;
        final BiometricSessionType sessionType;
        final long startTime;
        
        BiometricSession(String sessionId, BiometricSessionType sessionType, long startTime) {
            this.sessionId = sessionId;
            this.sessionType = sessionType;
            this.startTime = startTime;
        }
    }
}
