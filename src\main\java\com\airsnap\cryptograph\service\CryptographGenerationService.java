package com.airsnap.cryptograph.service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.airsnap.cryptograph.dao.CryptographDAO;
import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.BiometricData;
import com.airsnap.cryptograph.model.Cryptograph;
import com.airsnap.cryptograph.model.DemographicInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Service for generating cryptographs using AirSnap libraries
 */
public class CryptographGenerationService {
    private static final Logger logger = LoggerFactory.getLogger(CryptographGenerationService.class);
    
    private final AirSnapLibraryWrapper airSnapWrapper;
    private final CryptographDAO cryptographDAO;
    private final BiometricDataService biometricService;
    private final DemographicDataService demographicService;
    private final ObjectMapper objectMapper;
    
    public CryptographGenerationService(DatabaseManager dbManager) {
        this.airSnapWrapper = new AirSnapLibraryWrapper();
        this.cryptographDAO = new CryptographDAO(dbManager);
        this.biometricService = new BiometricDataService(dbManager);
        this.demographicService = new DemographicDataService(dbManager);
        this.objectMapper = new ObjectMapper();
        
        // Initialize AirSnap libraries
        initializeLibraries();
    }
    
    /**
     * Initialize all AirSnap libraries
     */
    private void initializeLibraries() {
        try {
            // Initialize T5 Client with license key (you'll need to provide the actual license)
            boolean t5Initialized = airSnapWrapper.initializeT5Client("YOUR_LICENSE_KEY_HERE");
            if (!t5Initialized) {
                throw new RuntimeException("Failed to initialize T5 Cryptograph Client");
            }
            
            // Initialize Cryptograph Reader
            boolean readerInitialized = airSnapWrapper.initializeCryptographReader();
            if (!readerInitialized) {
                throw new RuntimeException("Failed to initialize Cryptograph Reader");
            }
            
            // Initialize OmniMatch
            boolean omniMatchInitialized = airSnapWrapper.initializeOmniMatch();
            if (!omniMatchInitialized) {
                throw new RuntimeException("Failed to initialize OmniMatch");
            }
            
            // Verify system is ready
            if (!airSnapWrapper.isSystemReady()) {
                throw new RuntimeException("AirSnap system is not ready");
            }
            
            logger.info("All AirSnap libraries initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize AirSnap libraries", e);
            throw new RuntimeException("Library initialization failed", e);
        }
    }
    
    /**
     * Generate a cryptograph from demographic information and biometric data
     */
    public Cryptograph generateCryptograph(DemographicInfo demographicInfo, 
                                         byte[] faceImageData, 
                                         byte[] fingerprintImageData) throws Exception {
        
        logger.info("Starting cryptograph generation for: {}", demographicInfo.getFullName());
        
        try {
            // 1. Save demographic information
            Long demographicId = demographicService.saveDemographicInfo(demographicInfo);
            demographicInfo.setId(demographicId);
            
            // 2. Process biometric data
            BiometricData biometricData = processBiometricData(demographicId, faceImageData, fingerprintImageData);
            Long biometricId = biometricService.saveBiometricData(biometricData);
            biometricData.setId(biometricId);
            
            // 3. Convert demographic info to JSON for the native library
            String demographicJson = convertDemographicToJson(demographicInfo);
            
            // 4. Generate cryptograph using AirSnap library
            byte[] cryptographData = airSnapWrapper.safeGenerateCryptograph(
                demographicJson, faceImageData, fingerprintImageData);
            
            if (cryptographData == null || cryptographData.length == 0) {
                throw new RuntimeException("Failed to generate cryptograph - no data returned from native library");
            }
            
            // 5. Calculate hash of the cryptograph data
            String cryptographHash = calculateSHA256Hash(cryptographData);
            
            // 6. Create cryptograph object
            String cryptographId = generateCryptographId();
            Cryptograph cryptograph = new Cryptograph(
                cryptographId, demographicId, biometricId, 
                cryptographData, cryptographHash, "T5_AIRSNAP_V1"
            );
            
            // 7. Save cryptograph to database
            Long id = cryptographDAO.saveCryptograph(cryptograph);
            cryptograph.setId(id);
            cryptograph.setDemographicInfo(demographicInfo);
            cryptograph.setBiometricData(biometricData);
            
            logger.info("Cryptograph generated successfully with ID: {} (Size: {} bytes)", 
                       cryptographId, cryptographData.length);
            
            return cryptograph;
            
        } catch (Exception e) {
            logger.error("Error generating cryptograph for: {}", demographicInfo.getFullName(), e);
            throw new Exception("Cryptograph generation failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Process biometric data and extract templates
     */
    private BiometricData processBiometricData(Long demographicId, byte[] faceImageData, byte[] fingerprintImageData) {
        BiometricData biometricData = new BiometricData(demographicId);
        
        try {
            // Process face data
            if (faceImageData != null && faceImageData.length > 0) {
                byte[] faceTemplate = airSnapWrapper.extractFaceTemplate(faceImageData);
                biometricData.setFaceTemplate(faceTemplate);
                biometricData.setFaceImage(faceImageData);
                logger.info("Face template extracted successfully (Size: {} bytes)", 
                           faceTemplate != null ? faceTemplate.length : 0);
            }
            
            // Process fingerprint data
            if (fingerprintImageData != null && fingerprintImageData.length > 0) {
                byte[] fingerprintTemplate = airSnapWrapper.extractFingerprintTemplate(fingerprintImageData);
                byte[] fingerprintMinutiae = airSnapWrapper.extractFingerprintMinutiae(fingerprintImageData);
                
                biometricData.setFingerprintTemplate(fingerprintTemplate);
                biometricData.setFingerprintMinutiae(fingerprintMinutiae);
                biometricData.setFingerprintImage(fingerprintImageData);
                
                logger.info("Fingerprint template extracted successfully (Template: {} bytes, Minutiae: {} bytes)", 
                           fingerprintTemplate != null ? fingerprintTemplate.length : 0,
                           fingerprintMinutiae != null ? fingerprintMinutiae.length : 0);
            }
            
        } catch (Exception e) {
            logger.error("Error processing biometric data", e);
            throw new RuntimeException("Biometric processing failed", e);
        }
        
        return biometricData;
    }
    
    /**
     * Convert demographic information to JSON format for native library
     */
    private String convertDemographicToJson(DemographicInfo demographicInfo) throws Exception {
        Map<String, Object> demographicMap = new HashMap<>();
        demographicMap.put("firstName", demographicInfo.getFirstName());
        demographicMap.put("lastName", demographicInfo.getLastName());
        demographicMap.put("dateOfBirth", demographicInfo.getDateOfBirth() != null ? 
                          demographicInfo.getDateOfBirth().toString() : null);
        demographicMap.put("gender", demographicInfo.getGender());
        demographicMap.put("nationality", demographicInfo.getNationality());
        demographicMap.put("documentNumber", demographicInfo.getDocumentNumber());
        demographicMap.put("documentType", demographicInfo.getDocumentType());
        
        return objectMapper.writeValueAsString(demographicMap);
    }
    
    /**
     * Generate unique cryptograph ID
     */
    private String generateCryptographId() {
        return "CG-" + UUID.randomUUID().toString().toUpperCase().replace("-", "");
    }
    
    /**
     * Calculate SHA-256 hash of data
     */
    private String calculateSHA256Hash(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(data);
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString().toUpperCase();
    }
    
    /**
     * Get library version information
     */
    public String getLibraryVersions() {
        try {
            return airSnapWrapper.getLibraryVersions();
        } catch (Exception e) {
            logger.error("Error getting library versions", e);
            return "Error retrieving version information";
        }
    }


    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        try {
            airSnapWrapper.cleanup();
            logger.info("AirSnap library resources cleaned up");
        } catch (Exception e) {
            logger.error("Error cleaning up AirSnap resources", e);
        }
    }
}
