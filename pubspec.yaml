name: airsnap_biometric_app
description: AirSnap Biometric Cryptograph System with Flutter UI and Java Native Integration
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  
  # Camera & Image Processing
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3
  
  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # File Operations
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  
  # State Management
  provider: ^6.0.5
  
  # HTTP & JSON
  http: ^1.1.0
  json_annotation: ^4.8.1
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  crypto: ^3.0.3
  
  # Platform Integration
  flutter_native_splash: ^2.3.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700

flutter_native_splash:
  color: "#2196F3"
  image: assets/images/airsnap_logo.png
  android_12:
    image: assets/images/airsnap_logo.png
    color: "#2196F3"
