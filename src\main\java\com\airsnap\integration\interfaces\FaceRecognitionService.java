package com.airsnap.integration.interfaces;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * Interface for Face Recognition functionality
 * Represents AirsnapFaceUI and airsnap-face-pro-core libraries
 */
public interface FaceRecognitionService {
    
    /**
     * Initialize the face recognition engine
     * @return true if initialization successful
     */
    boolean initialize();
    
    /**
     * Detect faces in an image
     * @param image Input image
     * @return List of detected face regions
     */
    List<FaceDetectionResult> detectFaces(BufferedImage image);
    
    /**
     * Extract face features for recognition
     * @param image Face image
     * @return Face feature template
     */
    byte[] extractFaceFeatures(BufferedImage image);
    
    /**
     * Compare two face templates
     * @param template1 First face template
     * @param template2 Second face template
     * @return Similarity score (0.0 to 1.0)
     */
    double compareFaceTemplates(byte[] template1, byte[] template2);
    
    /**
     * Verify face against stored template
     * @param liveImage Live captured image
     * @param storedTemplate Stored face template
     * @param threshold Verification threshold
     * @return Verification result
     */
    FaceVerificationResult verifyFace(BufferedImage liveImage, byte[] storedTemplate, double threshold);
    
    /**
     * Perform liveness detection
     * @param image Input image
     * @return Liveness detection result
     */
    LivenessResult detectLiveness(BufferedImage image);
    
    /**
     * Get face quality score
     * @param image Face image
     * @return Quality score (0.0 to 1.0)
     */
    double getFaceQuality(BufferedImage image);
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Inner classes for results
    public static class FaceDetectionResult {
        private final int x, y, width, height;
        private final double confidence;
        
        public FaceDetectionResult(int x, int y, int width, int height, double confidence) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.confidence = confidence;
        }
        
        // Getters
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public double getConfidence() { return confidence; }
    }
    
    public static class FaceVerificationResult {
        private final boolean verified;
        private final double score;
        private final String message;
        
        public FaceVerificationResult(boolean verified, double score, String message) {
            this.verified = verified;
            this.score = score;
            this.message = message;
        }
        
        // Getters
        public boolean isVerified() { return verified; }
        public double getScore() { return score; }
        public String getMessage() { return message; }
    }
    
    public static class LivenessResult {
        private final boolean isLive;
        private final double confidence;
        private final Map<String, Object> details;
        
        public LivenessResult(boolean isLive, double confidence, Map<String, Object> details) {
            this.isLive = isLive;
            this.confidence = confidence;
            this.details = details;
        }
        
        // Getters
        public boolean isLive() { return isLive; }
        public double getConfidence() { return confidence; }
        public Map<String, Object> getDetails() { return details; }
    }
}
