["C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\My Projects\\java intergration\\airsnap_test\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]