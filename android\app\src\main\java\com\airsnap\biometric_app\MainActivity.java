package com.airsnap.biometric_app;

import android.os.Bundle;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "com.airsnap.biometric/native";
    private AirSnapBiometricHandler biometricHandler;

    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        
        // Initialize AirSnap biometric handler
        biometricHandler = new AirSnapBiometricHandler(this);
        
        // Set up method channel for Flutter-Java communication
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
            .setMethodCallHandler(
                (call, result) -> {
                    switch (call.method) {
                        case "initializeAirSnap":
                            biometricHandler.initializeAirSnap(result);
                            break;
                            
                        case "generateCryptograph":
                            String demographicData = call.argument("demographicData");
                            String faceImagePath = call.argument("faceImagePath");
                            String fingerprintImagePath = call.argument("fingerprintImagePath");
                            String fingerType = call.argument("fingerType");
                            biometricHandler.generateCryptograph(demographicData, faceImagePath, 
                                fingerprintImagePath, fingerType, result);
                            break;
                            
                        case "scanCryptograph":
                            String cryptographPath = call.argument("cryptographPath");
                            biometricHandler.scanCryptograph(cryptographPath, result);
                            break;
                            
                        case "compareFingerprints":
                            String fingerprint1Path = call.argument("fingerprint1Path");
                            String fingerprint2Path = call.argument("fingerprint2Path");
                            biometricHandler.compareFingerprints(fingerprint1Path, fingerprint2Path, result);
                            break;
                            
                        case "extractFaceTemplate":
                            String facePath = call.argument("facePath");
                            biometricHandler.extractFaceTemplate(facePath, result);
                            break;
                            
                        case "extractFingerprintTemplate":
                            String fingerprintPath = call.argument("fingerprintPath");
                            biometricHandler.extractFingerprintTemplate(fingerprintPath, result);
                            break;
                            
                        case "validateCryptographIntegrity":
                            String cryptographValidationPath = call.argument("cryptographPath");
                            biometricHandler.validateCryptographIntegrity(cryptographValidationPath, result);
                            break;
                            
                        case "getCryptographMetadata":
                            String metadataPath = call.argument("cryptographPath");
                            biometricHandler.getCryptographMetadata(metadataPath, result);
                            break;
                            
                        default:
                            result.notImplemented();
                            break;
                    }
                }
            );
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Request necessary permissions
        requestPermissions(new String[]{
            android.Manifest.permission.CAMERA,
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
            android.Manifest.permission.READ_EXTERNAL_STORAGE
        }, 1001);
    }
}
