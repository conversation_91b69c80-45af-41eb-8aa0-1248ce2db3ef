package com.airsnap.integration;

import com.airsnap.integration.impl.ComputerVisionServiceImpl;
import com.airsnap.integration.interfaces.ComputerVisionService;
import com.airsnap.integration.interfaces.ComputerVisionService.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test suite for Computer Vision Service
 */
public class ComputerVisionServiceTest {
    
    private ComputerVisionService cvService;
    private BufferedImage testImage;
    
    @BeforeEach
    void setUp() {
        cvService = new ComputerVisionServiceImpl();
        // Create a test image
        testImage = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
    }
    
    @Test
    @DisplayName("Service should initialize successfully")
    void testServiceInitialization() {
        assertTrue(cvService.initialize(), "Service should initialize successfully");
    }
    
    @Test
    @DisplayName("Should throw exception when service not initialized")
    void testUninitializedServiceThrowsException() {
        assertThrows(IllegalStateException.class, () -> {
            cvService.detectObjects(testImage);
        }, "Should throw IllegalStateException when service not initialized");
    }
    
    @Test
    @DisplayName("Should load neural network model successfully")
    void testLoadNeuralNetworkModel() {
        cvService.initialize();
        boolean result = cvService.loadNeuralNetworkModel("/test/model.onnx", ModelType.CLASSIFICATION);
        assertTrue(result, "Should load neural network model successfully");
    }
    
    @Test
    @DisplayName("Should process image with neural network")
    void testProcessWithNeuralNetwork() {
        cvService.initialize();
        cvService.loadNeuralNetworkModel("/test/model.onnx", ModelType.CLASSIFICATION);
        
        NeuralNetworkResult result = cvService.processWithNeuralNetwork(testImage, "model.onnx");
        
        assertNotNull(result, "Neural network result should not be null");
        assertNotNull(result.getOutput(), "Output array should not be null");
        assertTrue(result.getOutput().length > 0, "Output array should not be empty");
        assertNotNull(result.getPredictions(), "Predictions should not be null");
        assertTrue(result.getProcessingTime() >= 0, "Processing time should be non-negative");
    }
    
    @Test
    @DisplayName("Should detect objects in image")
    void testDetectObjects() {
        cvService.initialize();
        
        List<ObjectDetectionResult> objects = cvService.detectObjects(testImage);
        
        assertNotNull(objects, "Object detection result should not be null");
        assertTrue(objects.size() > 0, "Should detect at least one object");
        
        for (ObjectDetectionResult obj : objects) {
            assertNotNull(obj.getClassName(), "Object class name should not be null");
            assertTrue(obj.getConfidence() >= 0.0 && obj.getConfidence() <= 1.0, 
                      "Confidence should be between 0 and 1");
            assertTrue(obj.getX() >= 0, "X coordinate should be non-negative");
            assertTrue(obj.getY() >= 0, "Y coordinate should be non-negative");
            assertTrue(obj.getWidth() > 0, "Width should be positive");
            assertTrue(obj.getHeight() > 0, "Height should be positive");
        }
    }
    
    @Test
    @DisplayName("Should apply image filters")
    void testApplyImageFilters() {
        cvService.initialize();
        
        List<ImageFilter> filters = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("radius", 3);
        filters.add(new ImageFilter("Gaussian Blur", params));
        
        BufferedImage result = cvService.applyImageFilters(testImage, filters);
        
        assertNotNull(result, "Filtered image should not be null");
        assertEquals(testImage.getWidth(), result.getWidth(), "Width should be preserved");
        assertEquals(testImage.getHeight(), result.getHeight(), "Height should be preserved");
    }
    
    @Test
    @DisplayName("Should extract image features for all feature types")
    void testExtractImageFeatures() {
        cvService.initialize();
        
        for (FeatureType featureType : FeatureType.values()) {
            float[] features = cvService.extractImageFeatures(testImage, featureType);
            
            assertNotNull(features, "Features should not be null for " + featureType);
            assertTrue(features.length > 0, "Feature vector should not be empty for " + featureType);
            
            // Check expected feature sizes
            switch (featureType) {
                case SIFT:
                    assertEquals(128, features.length, "SIFT should have 128 features");
                    break;
                case SURF:
                    assertEquals(64, features.length, "SURF should have 64 features");
                    break;
                case ORB:
                case BRIEF:
                    assertEquals(32, features.length, "ORB/BRIEF should have 32 features");
                    break;
                case HARRIS_CORNERS:
                    assertEquals(16, features.length, "Harris corners should have 16 features");
                    break;
                case FAST:
                    assertEquals(8, features.length, "FAST should have 8 features");
                    break;
            }
        }
    }
    
    @Test
    @DisplayName("Should perform image segmentation for all segmentation types")
    void testPerformImageSegmentation() {
        cvService.initialize();
        
        for (SegmentationType segmentationType : SegmentationType.values()) {
            BufferedImage result = cvService.performImageSegmentation(testImage, segmentationType);
            
            assertNotNull(result, "Segmented image should not be null for " + segmentationType);
            assertEquals(testImage.getWidth(), result.getWidth(), 
                        "Width should be preserved for " + segmentationType);
            assertEquals(testImage.getHeight(), result.getHeight(), 
                        "Height should be preserved for " + segmentationType);
        }
    }
    
    @Test
    @DisplayName("Should calculate image similarity for all metrics")
    void testCalculateImageSimilarity() {
        cvService.initialize();
        
        BufferedImage image2 = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
        
        for (SimilarityMetric metric : SimilarityMetric.values()) {
            double similarity = cvService.calculateImageSimilarity(testImage, image2, metric);
            
            assertTrue(similarity >= 0.0 && similarity <= 1.0, 
                      "Similarity should be between 0 and 1 for " + metric);
        }
    }
    
    @Test
    @DisplayName("Should enhance image quality for all enhancement types")
    void testEnhanceImageQuality() {
        cvService.initialize();
        
        for (EnhancementType enhancementType : EnhancementType.values()) {
            BufferedImage result = cvService.enhanceImageQuality(testImage, enhancementType);
            
            assertNotNull(result, "Enhanced image should not be null for " + enhancementType);
            assertEquals(testImage.getWidth(), result.getWidth(), 
                        "Width should be preserved for " + enhancementType);
            assertEquals(testImage.getHeight(), result.getHeight(), 
                        "Height should be preserved for " + enhancementType);
        }
    }
    
    @Test
    @DisplayName("Should perform OCR and return text")
    void testPerformOCR() {
        cvService.initialize();
        
        String result = cvService.performOCR(testImage);
        
        assertNotNull(result, "OCR result should not be null");
        assertFalse(result.trim().isEmpty(), "OCR result should not be empty");
    }
    
    @Test
    @DisplayName("Should return processing statistics")
    void testGetProcessingStatistics() {
        cvService.initialize();
        
        // Perform some operations to generate statistics
        cvService.detectObjects(testImage);
        cvService.extractImageFeatures(testImage, FeatureType.SIFT);
        
        ProcessingStatistics stats = cvService.getProcessingStatistics();
        
        assertNotNull(stats, "Statistics should not be null");
        assertTrue(stats.getTotalProcessedImages() >= 0, "Total processed images should be non-negative");
        assertTrue(stats.getAverageProcessingTime() >= 0, "Average processing time should be non-negative");
        assertNotNull(stats.getOperationCounts(), "Operation counts should not be null");
        assertTrue(stats.getOperationCounts().size() > 0, "Should have operation counts");
    }
    
    @Test
    @DisplayName("Should cleanup resources properly")
    void testCleanup() {
        cvService.initialize();
        
        // Perform some operations
        cvService.detectObjects(testImage);
        
        // Cleanup should not throw exceptions
        assertDoesNotThrow(() -> cvService.cleanup(), "Cleanup should not throw exceptions");
        
        // After cleanup, operations should fail
        assertThrows(IllegalStateException.class, () -> {
            cvService.detectObjects(testImage);
        }, "Operations should fail after cleanup");
    }
    
    @Test
    @DisplayName("Should handle unsupported feature type")
    void testUnsupportedFeatureType() {
        cvService.initialize();
        
        // This test would require adding an invalid enum value, 
        // but since we can't modify enums in tests, we'll test the existing ones
        // The implementation should handle all defined enum values
        for (FeatureType featureType : FeatureType.values()) {
            assertDoesNotThrow(() -> {
                cvService.extractImageFeatures(testImage, featureType);
            }, "Should handle all defined feature types: " + featureType);
        }
    }
}
