package com.airsnap.cryptograph.service;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.BiometricData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for managing biometric data
 */
public class BiometricDataService {
    private static final Logger logger = LoggerFactory.getLogger(BiometricDataService.class);
    private final DatabaseManager dbManager;
    
    public BiometricDataService(DatabaseManager dbManager) {
        this.dbManager = dbManager;
    }
    
    /**
     * Save biometric data to database
     */
    public Long saveBiometricData(BiometricData biometricData) throws SQLException {
        String sql = """
            INSERT INTO biometric_data (demographic_id, face_template, face_image, 
                                      fingerprint_template, fingerprint_image, fingerprint_minutiae)
            VALUES (?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            stmt.setLong(1, biometricData.getDemographicId());
            stmt.setBytes(2, biometricData.getFaceTemplate());
            stmt.setBytes(3, biometricData.getFaceImage());
            stmt.setBytes(4, biometricData.getFingerprintTemplate());
            stmt.setBytes(5, biometricData.getFingerprintImage());
            stmt.setBytes(6, biometricData.getFingerprintMinutiae());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating biometric data failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    Long id = generatedKeys.getLong(1);
                    biometricData.setId(id);
                    biometricData.setCreatedAt(LocalDateTime.now());
                    
                    // Log audit
                    dbManager.logAudit("CREATE", "BIOMETRIC", id.toString(), 
                                     "SYSTEM", "Biometric data created", true, null);
                    
                    logger.info("Biometric data saved with ID: {} for demographic ID: {}", 
                               id, biometricData.getDemographicId());
                    return id;
                } else {
                    throw new SQLException("Creating biometric data failed, no ID obtained.");
                }
            }
        }
    }
    
    /**
     * Find biometric data by ID
     */
    public BiometricData findById(Long id) throws SQLException {
        String sql = "SELECT * FROM biometric_data WHERE id = ?";
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToBiometricData(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Find biometric data by demographic ID
     */
    public BiometricData findByDemographicId(Long demographicId) throws SQLException {
        String sql = "SELECT * FROM biometric_data WHERE demographic_id = ?";
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, demographicId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToBiometricData(rs);
                }
            }
        }
        return null;
    }
    
    /**
     * Get all biometric data
     */
    public List<BiometricData> findAll() throws SQLException {
        String sql = "SELECT * FROM biometric_data ORDER BY created_at DESC";
        
        List<BiometricData> results = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                results.add(mapResultSetToBiometricData(rs));
            }
        }
        return results;
    }
    
    /**
     * Update biometric data
     */
    public boolean updateBiometricData(BiometricData biometricData) throws SQLException {
        String sql = """
            UPDATE biometric_data 
            SET face_template = ?, face_image = ?, fingerprint_template = ?, 
                fingerprint_image = ?, fingerprint_minutiae = ?
            WHERE id = ?
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setBytes(1, biometricData.getFaceTemplate());
            stmt.setBytes(2, biometricData.getFaceImage());
            stmt.setBytes(3, biometricData.getFingerprintTemplate());
            stmt.setBytes(4, biometricData.getFingerprintImage());
            stmt.setBytes(5, biometricData.getFingerprintMinutiae());
            stmt.setLong(6, biometricData.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                // Log audit
                dbManager.logAudit("UPDATE", "BIOMETRIC", biometricData.getId().toString(), 
                                 "SYSTEM", "Biometric data updated", true, null);
                
                logger.info("Biometric data updated for ID: {}", biometricData.getId());
                return true;
            }
        }
        return false;
    }
    
    /**
     * Delete biometric data
     */
    public boolean deleteBiometricData(Long id) throws SQLException {
        BiometricData biometricData = findById(id);
        if (biometricData == null) {
            return false;
        }
        
        String sql = "DELETE FROM biometric_data WHERE id = ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setLong(1, id);
            int affectedRows = stmt.executeUpdate();
            
            if (affectedRows > 0) {
                // Log audit
                dbManager.logAudit("DELETE", "BIOMETRIC", id.toString(), 
                                 "SYSTEM", "Biometric data deleted", true, null);
                
                logger.info("Biometric data deleted for ID: {}", id);
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get biometric data statistics
     */
    public BiometricStats getBiometricStats() throws SQLException {
        String sql = """
            SELECT 
                COUNT(*) as total_records,
                SUM(CASE WHEN face_template IS NOT NULL THEN 1 ELSE 0 END) as face_records,
                SUM(CASE WHEN fingerprint_template IS NOT NULL THEN 1 ELSE 0 END) as fingerprint_records,
                AVG(LENGTH(face_template)) as avg_face_template_size,
                AVG(LENGTH(fingerprint_template)) as avg_fingerprint_template_size
            FROM biometric_data
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return new BiometricStats(
                    rs.getInt("total_records"),
                    rs.getInt("face_records"),
                    rs.getInt("fingerprint_records"),
                    rs.getDouble("avg_face_template_size"),
                    rs.getDouble("avg_fingerprint_template_size")
                );
            }
        }
        return new BiometricStats(0, 0, 0, 0.0, 0.0);
    }
    
    /**
     * Map ResultSet to BiometricData object
     */
    private BiometricData mapResultSetToBiometricData(ResultSet rs) throws SQLException {
        BiometricData biometricData = new BiometricData();
        biometricData.setId(rs.getLong("id"));
        biometricData.setDemographicId(rs.getLong("demographic_id"));
        biometricData.setFaceTemplate(rs.getBytes("face_template"));
        biometricData.setFaceImage(rs.getBytes("face_image"));
        biometricData.setFingerprintTemplate(rs.getBytes("fingerprint_template"));
        biometricData.setFingerprintImage(rs.getBytes("fingerprint_image"));
        biometricData.setFingerprintMinutiae(rs.getBytes("fingerprint_minutiae"));
        biometricData.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        
        return biometricData;
    }
    
    /**
     * Inner class for biometric statistics
     */
    public static class BiometricStats {
        private final int totalRecords;
        private final int faceRecords;
        private final int fingerprintRecords;
        private final double avgFaceTemplateSize;
        private final double avgFingerprintTemplateSize;
        
        public BiometricStats(int totalRecords, int faceRecords, int fingerprintRecords, 
                            double avgFaceTemplateSize, double avgFingerprintTemplateSize) {
            this.totalRecords = totalRecords;
            this.faceRecords = faceRecords;
            this.fingerprintRecords = fingerprintRecords;
            this.avgFaceTemplateSize = avgFaceTemplateSize;
            this.avgFingerprintTemplateSize = avgFingerprintTemplateSize;
        }
        
        // Getters
        public int getTotalRecords() { return totalRecords; }
        public int getFaceRecords() { return faceRecords; }
        public int getFingerprintRecords() { return fingerprintRecords; }
        public double getAvgFaceTemplateSize() { return avgFaceTemplateSize; }
        public double getAvgFingerprintTemplateSize() { return avgFingerprintTemplateSize; }
        
        @Override
        public String toString() {
            return String.format("BiometricStats{total=%d, face=%d, fingerprint=%d, avgFaceSize=%.1f, avgFingerprintSize=%.1f}", 
                               totalRecords, faceRecords, fingerprintRecords, avgFaceTemplateSize, avgFingerprintTemplateSize);
        }
    }
}
