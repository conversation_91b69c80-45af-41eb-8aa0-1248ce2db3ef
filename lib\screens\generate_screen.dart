import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../models/biometric_models.dart';
import '../providers/biometric_provider.dart';
import '../widgets/face_capture_widget.dart';
import '../widgets/fingerprint_capture_widget.dart';
import '../services/camera_service.dart';
import 'home_screen.dart';

class GenerateScreen extends StatefulWidget {
  const GenerateScreen({super.key});

  @override
  State<GenerateScreen> createState() => _GenerateScreenState();
}

class _GenerateScreenState extends State<GenerateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  final _nationalityController = TextEditingController();
  final _documentNumberController = TextEditingController();
  
  Gender _selectedGender = Gender.male;
  FingerType _selectedFingerType = FingerType.rightIndex;
  String _selectedDocumentType = 'Passport';
  
  File? _faceImage;
  File? _fingerprintImage;
  
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _dateOfBirthController.dispose();
    _nationalityController.dispose();
    _documentNumberController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime(1990),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      _dateOfBirthController.text = picked.toIso8601String().split('T')[0];
    }
  }

  Future<void> _captureOrSelectFaceImage() async {
    // Initialize camera service
    await CameraService.initialize();

    if (!mounted) return;

    final File? faceFile = await showDialog<File>(
      context: context,
      builder: (context) => FaceCaptureDialog(
        onFaceCaptured: (File file) => file,
      ),
    );

    if (faceFile != null) {
      setState(() {
        _faceImage = faceFile;
      });
    }
  }

  Future<void> _captureOrSelectFingerprintImage() async {
    // Initialize camera service
    await CameraService.initialize();

    if (!mounted) return;

    final Map<String, dynamic>? result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => FingerprintCaptureDialog(
        onFingerprintCaptured: (File file, FingerType type) => {
          'file': file,
          'type': type,
        },
      ),
    );

    if (result != null) {
      setState(() {
        _fingerprintImage = result['file'] as File;
        _selectedFingerType = (result['type'] as FingerType).displayName;
      });
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Image Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateCryptograph() async {
    if (!_formKey.currentState!.validate()) return;
    if (_faceImage == null) {
      _showError('Please capture or select a face image');
      return;
    }
    if (_fingerprintImage == null) {
      _showError('Please capture or select a fingerprint image');
      return;
    }

    final demographicData = DemographicData(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      dateOfBirth: _dateOfBirthController.text.trim(),
      nationality: _nationalityController.text.trim(),
      gender: _selectedGender.displayName,
      documentNumber: _documentNumberController.text.trim(),
      documentType: _selectedDocumentType,
    );

    final provider = context.read<BiometricProvider>();
    final success = await provider.generateCryptograph(
      demographicData: demographicData,
      faceImagePath: _faceImage!.path,
      fingerprintImagePath: _fingerprintImage!.path,
      fingerType: _selectedFingerType.displayName,
    );

    if (success && mounted) {
      _showSuccessDialog();
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessDialog() {
    final result = context.read<BiometricProvider>().lastGeneratedCryptograph;
    if (result != null) {
      showDialog(
        context: context,
        builder: (context) => SuccessDialog(
          title: 'Cryptograph Generated',
          message: 'Cryptograph ${result.cryptographId} has been generated successfully!',
          onOk: _clearForm,
        ),
      );
    }
  }

  void _clearForm() {
    _firstNameController.clear();
    _lastNameController.clear();
    _dateOfBirthController.clear();
    _nationalityController.clear();
    _documentNumberController.clear();
    setState(() {
      _faceImage = null;
      _fingerprintImage = null;
      _selectedGender = Gender.male;
      _selectedFingerType = FingerType.rightIndex;
      _selectedDocumentType = 'Passport';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate Cryptograph'),
        actions: [
          IconButton(
            onPressed: _clearForm,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear Form',
          ),
        ],
      ),
      body: Consumer<BiometricProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Error banner
                      if (provider.hasError)
                        ErrorBanner(
                          message: provider.errorMessage!,
                          onDismiss: provider.clearError,
                        ),

                      // Personal Information Section
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Personal Information',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: _firstNameController,
                                      decoration: const InputDecoration(
                                        labelText: 'First Name',
                                        prefixIcon: Icon(Icons.person),
                                      ),
                                      validator: (value) {
                                        if (value?.isEmpty ?? true) {
                                          return 'Please enter first name';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: TextFormField(
                                      controller: _lastNameController,
                                      decoration: const InputDecoration(
                                        labelText: 'Last Name',
                                        prefixIcon: Icon(Icons.person_outline),
                                      ),
                                      validator: (value) {
                                        if (value?.isEmpty ?? true) {
                                          return 'Please enter last name';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              TextFormField(
                                controller: _dateOfBirthController,
                                decoration: const InputDecoration(
                                  labelText: 'Date of Birth',
                                  prefixIcon: Icon(Icons.calendar_today),
                                  suffixIcon: Icon(Icons.arrow_drop_down),
                                ),
                                readOnly: true,
                                onTap: _selectDate,
                                validator: (value) {
                                  if (value?.isEmpty ?? true) {
                                    return 'Please select date of birth';
                                  }
                                  return null;
                                },
                              ),
                              
                              const SizedBox(height: 16),
                              
                              Row(
                                children: [
                                  Expanded(
                                    child: DropdownButtonFormField<Gender>(
                                      value: _selectedGender,
                                      decoration: const InputDecoration(
                                        labelText: 'Gender',
                                        prefixIcon: Icon(Icons.wc),
                                      ),
                                      items: Gender.values.map((gender) {
                                        return DropdownMenuItem(
                                          value: gender,
                                          child: Text(gender.displayName),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          setState(() {
                                            _selectedGender = value;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: TextFormField(
                                      controller: _nationalityController,
                                      decoration: const InputDecoration(
                                        labelText: 'Nationality',
                                        prefixIcon: Icon(Icons.flag),
                                      ),
                                      validator: (value) {
                                        if (value?.isEmpty ?? true) {
                                          return 'Please enter nationality';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              Row(
                                children: [
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedDocumentType,
                                      decoration: const InputDecoration(
                                        labelText: 'Document Type',
                                        prefixIcon: Icon(Icons.description),
                                      ),
                                      items: ['Passport', 'ID Card', 'Driver License']
                                          .map((type) => DropdownMenuItem(
                                                value: type,
                                                child: Text(type),
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          setState(() {
                                            _selectedDocumentType = value;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: TextFormField(
                                      controller: _documentNumberController,
                                      decoration: const InputDecoration(
                                        labelText: 'Document Number',
                                        prefixIcon: Icon(Icons.numbers),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Biometric Data Section
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Biometric Data',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              // Face Image
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Container(
                                          width: 120,
                                          height: 120,
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: _faceImage != null
                                              ? ClipRRect(
                                                  borderRadius: BorderRadius.circular(8),
                                                  child: Image.file(
                                                    _faceImage!,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )
                                              : const Icon(
                                                  Icons.face,
                                                  size: 48,
                                                  color: Colors.grey,
                                                ),
                                        ),
                                        const SizedBox(height: 8),
                                        ElevatedButton.icon(
                                          onPressed: _captureOrSelectFaceImage,
                                          icon: const Icon(Icons.camera_alt),
                                          label: const Text('Face Photo'),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Container(
                                          width: 120,
                                          height: 120,
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: _fingerprintImage != null
                                              ? ClipRRect(
                                                  borderRadius: BorderRadius.circular(8),
                                                  child: Image.file(
                                                    _fingerprintImage!,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )
                                              : const Icon(
                                                  Icons.fingerprint,
                                                  size: 48,
                                                  color: Colors.grey,
                                                ),
                                        ),
                                        const SizedBox(height: 8),
                                        ElevatedButton.icon(
                                          onPressed: _captureOrSelectFingerprintImage,
                                          icon: const Icon(Icons.fingerprint),
                                          label: const Text('Fingerprint'),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              DropdownButtonFormField<FingerType>(
                                value: _selectedFingerType,
                                decoration: const InputDecoration(
                                  labelText: 'Finger Type',
                                  prefixIcon: Icon(Icons.touch_app),
                                ),
                                items: FingerType.values.map((finger) {
                                  return DropdownMenuItem(
                                    value: finger,
                                    child: Text(finger.displayName),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedFingerType = value;
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Generate Button
                      ElevatedButton.icon(
                        onPressed: provider.isGenerating ? null : _generateCryptograph,
                        icon: provider.isGenerating
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.security),
                        label: Text(
                          provider.isGenerating ? 'Generating...' : 'Generate Cryptograph',
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Loading overlay
              if (provider.isGenerating)
                const LoadingOverlay(
                  message: 'Generating cryptograph...\nThis may take a few moments.',
                ),
            ],
          );
        },
      ),
    );
  }
}
