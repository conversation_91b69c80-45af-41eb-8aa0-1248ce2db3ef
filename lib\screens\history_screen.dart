import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/biometric_models.dart';
import '../providers/biometric_provider.dart';
import 'home_screen.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BiometricProvider>().refreshAllData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _refreshData() async {
    await context.read<BiometricProvider>().refreshAllData();
  }

  void _searchCryptographs(String query) {
    context.read<BiometricProvider>().searchCryptographs(query);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.security),
              text: 'Cryptographs',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'Operations',
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Consumer<BiometricProvider>(
        builder: (context, provider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildCryptographsTab(provider),
              _buildOperationsTab(provider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCryptographsTab(BiometricProvider provider) {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search cryptographs by name...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _searchCryptographs('');
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            onChanged: _searchCryptographs,
          ),
        ),

        // Error banner
        if (provider.hasError)
          ErrorBanner(
            message: provider.errorMessage!,
            onDismiss: provider.clearError,
          ),

        // Cryptographs list
        Expanded(
          child: provider.cryptographs.isEmpty
              ? _buildEmptyState(
                  icon: Icons.security,
                  title: 'No Cryptographs Found',
                  subtitle: 'Generate your first cryptograph to see it here.',
                )
              : RefreshIndicator(
                  onRefresh: _refreshData,
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: provider.cryptographs.length,
                    itemBuilder: (context, index) {
                      final record = provider.cryptographs[index];
                      return _buildCryptographCard(record, provider);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildOperationsTab(BiometricProvider provider) {
    return Column(
      children: [
        // Stats card
        Padding(
          padding: const EdgeInsets.all(16),
          child: FutureBuilder<Map<String, int>>(
            future: provider.getDatabaseStats(),
            builder: (context, snapshot) {
              final stats = snapshot.data ?? {'cryptographs': 0, 'operations': 0};
              return Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'Cryptographs',
                      value: stats['cryptographs'].toString(),
                      icon: Icons.security,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatsCard(
                      title: 'Operations',
                      value: stats['operations'].toString(),
                      icon: Icons.history,
                      color: Colors.green,
                    ),
                  ),
                ],
              );
            },
          ),
        ),

        // Error banner
        if (provider.hasError)
          ErrorBanner(
            message: provider.errorMessage!,
            onDismiss: provider.clearError,
          ),

        // Operations list
        Expanded(
          child: provider.isLoadingHistory
              ? const Center(child: CircularProgressIndicator())
              : provider.operationHistory.isEmpty
                  ? _buildEmptyState(
                      icon: Icons.history,
                      title: 'No Operations Found',
                      subtitle: 'Your operation history will appear here.',
                    )
                  : RefreshIndicator(
                      onRefresh: _refreshData,
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: provider.operationHistory.length,
                        itemBuilder: (context, index) {
                          final operation = provider.operationHistory[index];
                          return _buildOperationCard(operation);
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildCryptographCard(CryptographRecord record, BiometricProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.security,
                    color: Colors.blue[600],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        record.cryptographId,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Finger: ${record.fingerType}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    switch (value) {
                      case 'view':
                        _viewCryptographDetails(record);
                        break;
                      case 'delete':
                        _deleteCryptograph(record, provider);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(Icons.visibility),
                          SizedBox(width: 8),
                          Text('View Details'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Created: ${record.createdAt}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            if (record.notes?.isNotEmpty ?? false) ...[
              const SizedBox(height: 4),
              Text(
                'Notes: ${record.notes}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _viewCryptographDetails(record),
                    icon: const Icon(Icons.visibility),
                    label: const Text('View'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () => _downloadCryptograph(record),
                  icon: const Icon(Icons.download),
                  label: const Text('Download'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationCard(OperationHistory operation) {
    final isSuccess = operation.success;
    final operationIcon = _getOperationIcon(operation.operationType);
    final operationColor = isSuccess ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: operationColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                operationIcon,
                color: operationColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getOperationDisplayName(operation.operationType),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    operation.result,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    operation.timestamp,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: operationColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _viewCryptographDetails(CryptographRecord record) {
    showDialog(
      context: context,
      builder: (context) => CryptographDetailsDialog(record: record),
    );
  }

  void _deleteCryptograph(CryptographRecord record, BiometricProvider provider) {
    showDialog(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Cryptograph',
        message: 'Are you sure you want to delete cryptograph ${record.cryptographId}? This action cannot be undone.',
        onConfirm: () async {
          final success = await provider.deleteCryptograph(record.id!);
          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Cryptograph deleted successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
      ),
    );
  }

  void _downloadCryptograph(CryptographRecord record) {
    // In a real implementation, this would handle file sharing/downloading
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading ${record.cryptographId}...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  IconData _getOperationIcon(String operationType) {
    switch (operationType) {
      case 'INSERT_CRYPTOGRAPH':
      case 'GENERATE_CRYPTOGRAPH':
        return Icons.add_photo_alternate;
      case 'SCAN_CRYPTOGRAPH':
        return Icons.qr_code_scanner;
      case 'COMPARE_FINGERPRINTS':
        return Icons.compare_arrows;
      case 'DELETE_CRYPTOGRAPH':
        return Icons.delete;
      default:
        return Icons.history;
    }
  }

  String _getOperationDisplayName(String operationType) {
    switch (operationType) {
      case 'INSERT_CRYPTOGRAPH':
        return 'Cryptograph Generated';
      case 'GENERATE_CRYPTOGRAPH':
        return 'Generate Cryptograph';
      case 'SCAN_CRYPTOGRAPH':
        return 'Scan Cryptograph';
      case 'COMPARE_FINGERPRINTS':
        return 'Compare Fingerprints';
      case 'DELETE_CRYPTOGRAPH':
        return 'Delete Cryptograph';
      case 'GET_ALL_CRYPTOGRAPHS':
        return 'Load Cryptographs';
      case 'SEARCH_CRYPTOGRAPHS':
        return 'Search Cryptographs';
      default:
        return operationType.replaceAll('_', ' ').toLowerCase();
    }
  }
}

class CryptographDetailsDialog extends StatelessWidget {
  final CryptographRecord record;

  const CryptographDetailsDialog({
    super.key,
    required this.record,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.blue[600],
          ),
          const SizedBox(width: 8),
          const Text('Cryptograph Details'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailItem('Cryptograph ID', record.cryptographId),
            _buildDetailItem('Finger Type', record.fingerType),
            _buildDetailItem('Created At', record.createdAt),
            _buildDetailItem('File Path', record.cryptographPath),
            if (record.notes?.isNotEmpty ?? false)
              _buildDetailItem('Notes', record.notes!),
            
            const SizedBox(height: 16),
            
            const Text(
              'Demographic Data:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            // Parse and display demographic data
            Text(
              record.demographicData,
              style: const TextStyle(fontSize: 14),
            ),
            
            const SizedBox(height: 16),
            
            // Preview cryptograph file if it exists
            if (File(record.cryptographPath).existsSync()) ...[
              const Text(
                'Cryptograph Preview:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    File(record.cryptographPath),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
