package com.airsnap.integration.interfaces;

import java.awt.image.BufferedImage;
import java.util.List;

/**
 * Interface for Fingerprint functionality
 * Represents AirsnapFingerUI library
 */
public interface FingerprintService {
    
    /**
     * Initialize the fingerprint service
     * @return true if initialization successful
     */
    boolean initialize();
    
    /**
     * Capture fingerprint from device
     * @return Captured fingerprint image
     */
    BufferedImage captureFingerprint();
    
    /**
     * Extract fingerprint minutiae
     * @param fingerprintImage Fingerprint image
     * @return Minutiae template
     */
    byte[] extractMinutiae(BufferedImage fingerprintImage);
    
    /**
     * Compare two fingerprint templates
     * @param template1 First fingerprint template
     * @param template2 Second fingerprint template
     * @return Match score (0.0 to 1.0)
     */
    double compareFingerprints(byte[] template1, byte[] template2);
    
    /**
     * Verify fingerprint against stored template
     * @param liveFingerprint Live captured fingerprint
     * @param storedTemplate Stored fingerprint template
     * @param threshold Verification threshold
     * @return Verification result
     */
    FingerprintVerificationResult verifyFingerprint(BufferedImage liveFingerprint, byte[] storedTemplate, double threshold);
    
    /**
     * Get fingerprint quality score
     * @param fingerprintImage Fingerprint image
     * @return Quality score (0.0 to 1.0)
     */
    double getFingerprintQuality(BufferedImage fingerprintImage);
    
    /**
     * Detect fingerprint features
     * @param fingerprintImage Fingerprint image
     * @return List of detected features
     */
    List<FingerprintFeature> detectFeatures(BufferedImage fingerprintImage);
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Inner classes for results
    public static class FingerprintVerificationResult {
        private final boolean verified;
        private final double score;
        private final String message;
        private final int matchedMinutiae;
        
        public FingerprintVerificationResult(boolean verified, double score, String message, int matchedMinutiae) {
            this.verified = verified;
            this.score = score;
            this.message = message;
            this.matchedMinutiae = matchedMinutiae;
        }
        
        // Getters
        public boolean isVerified() { return verified; }
        public double getScore() { return score; }
        public String getMessage() { return message; }
        public int getMatchedMinutiae() { return matchedMinutiae; }
    }
    
    public static class FingerprintFeature {
        private final int x, y;
        private final double angle;
        private final String type;
        
        public FingerprintFeature(int x, int y, double angle, String type) {
            this.x = x;
            this.y = y;
            this.angle = angle;
            this.type = type;
        }
        
        // Getters
        public int getX() { return x; }
        public int getY() { return y; }
        public double getAngle() { return angle; }
        public String getType() { return type; }
    }
}
