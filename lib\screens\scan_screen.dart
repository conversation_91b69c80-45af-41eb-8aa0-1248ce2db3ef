import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../models/biometric_models.dart';
import '../providers/biometric_provider.dart';
import 'home_screen.dart';

class ScanScreen extends StatefulWidget {
  const ScanScreen({super.key});

  @override
  State<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends State<ScanScreen> {
  File? _cryptographImage;
  final ImagePicker _picker = ImagePicker();

  Future<void> _selectCryptographImage() async {
    final source = await _showImageSourceDialog();
    if (source != null) {
      final XFile? image = await _picker.pickImage(source: source);
      if (image != null) {
        setState(() {
          _cryptographImage = File(image.path);
        });
      }
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Cryptograph Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              subtitle: const Text('Scan cryptograph with camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              subtitle: const Text('Select cryptograph from gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _scanCryptograph() async {
    if (_cryptographImage == null) {
      _showError('Please select a cryptograph image to scan');
      return;
    }

    final provider = context.read<BiometricProvider>();
    final success = await provider.scanCryptograph(_cryptographImage!.path);

    if (success && mounted) {
      _showScanResults();
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showScanResults() {
    final result = context.read<BiometricProvider>().lastScanResult;
    if (result != null) {
      showDialog(
        context: context,
        builder: (context) => ScanResultDialog(result: result),
      );
    }
  }

  void _clearSelection() {
    setState(() {
      _cryptographImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Cryptograph'),
        actions: [
          IconButton(
            onPressed: _clearSelection,
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Selection',
          ),
        ],
      ),
      body: Consumer<BiometricProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Error banner
                    if (provider.hasError)
                      ErrorBanner(
                        message: provider.errorMessage!,
                        onDismiss: provider.clearError,
                      ),

                    // Instructions Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.blue[600],
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'How to Scan',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              '1. Select a cryptograph image from your camera or gallery\n'
                              '2. Ensure the cryptograph is clearly visible and well-lit\n'
                              '3. Tap "Scan Cryptograph" to extract the information\n'
                              '4. View the extracted demographic and biometric data',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Cryptograph Selection Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Select Cryptograph',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Center(
                              child: Container(
                                width: double.infinity,
                                height: 300,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey[300]!,
                                    width: 2,
                                    style: BorderStyle.solid,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: _cryptographImage != null
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.file(
                                          _cryptographImage!,
                                          fit: BoxFit.contain,
                                        ),
                                      )
                                    : Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.qr_code_2,
                                            size: 64,
                                            color: Colors.grey[400],
                                          ),
                                          const SizedBox(height: 16),
                                          Text(
                                            'No cryptograph selected',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            'Tap the button below to select',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[500],
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                            
                            const SizedBox(height: 16),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _selectCryptographImage,
                                    icon: const Icon(Icons.add_photo_alternate),
                                    label: const Text('Select Cryptograph'),
                                  ),
                                ),
                                if (_cryptographImage != null) ...[
                                  const SizedBox(width: 12),
                                  ElevatedButton.icon(
                                    onPressed: _clearSelection,
                                    icon: const Icon(Icons.clear),
                                    label: const Text('Clear'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Scan Button
                    ElevatedButton.icon(
                      onPressed: provider.isScanning || _cryptographImage == null
                          ? null
                          : _scanCryptograph,
                      icon: provider.isScanning
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.qr_code_scanner),
                      label: Text(
                        provider.isScanning ? 'Scanning...' : 'Scan Cryptograph',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Recent Scan Results
                    if (provider.lastScanResult != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.history,
                                    color: Colors.green[600],
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Last Scan Result',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              _buildScanResultSummary(provider.lastScanResult!),
                              const SizedBox(height: 12),
                              ElevatedButton.icon(
                                onPressed: () => _showScanResults(),
                                icon: const Icon(Icons.visibility),
                                label: const Text('View Full Details'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              // Loading overlay
              if (provider.isScanning)
                const LoadingOverlay(
                  message: 'Scanning cryptograph...\nExtracting biometric data.',
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildScanResultSummary(ScanResult result) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Name: ${result.demographicData.fullName}',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'DOB: ${result.demographicData.dateOfBirth}',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'Nationality: ${result.demographicData.nationality}',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'Finger Type: ${result.fingerType}',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          'Scanned: ${result.scannedAt}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}

class ScanResultDialog extends StatelessWidget {
  final ScanResult result;

  const ScanResultDialog({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green[600],
          ),
          const SizedBox(width: 8),
          const Text('Scan Results'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildInfoSection('Personal Information', [
              'Name: ${result.demographicData.fullName}',
              'Date of Birth: ${result.demographicData.dateOfBirth}',
              'Gender: ${result.demographicData.gender}',
              'Nationality: ${result.demographicData.nationality}',
            ]),
            
            const SizedBox(height: 16),
            
            _buildInfoSection('Document Information', [
              'Document Type: ${result.demographicData.documentType}',
              'Document Number: ${result.demographicData.documentNumber}',
              'Issuing Country: ${result.demographicData.issuingCountry}',
            ]),
            
            const SizedBox(height: 16),
            
            _buildInfoSection('Biometric Information', [
              'Cryptograph ID: ${result.cryptographId}',
              'Finger Type: ${result.fingerType}',
              'Face Template: ${result.faceTemplatePresent ? "Present" : "Not Present"}',
              'Fingerprint Template: ${result.fingerprintTemplatePresent ? "Present" : "Not Present"}',
            ]),
            
            const SizedBox(height: 16),
            
            _buildInfoSection('Scan Information', [
              'Scanned At: ${result.scannedAt}',
              'Status: Success',
            ]),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildInfoSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 4),
          child: Text(
            item,
            style: const TextStyle(fontSize: 14),
          ),
        )),
      ],
    );
  }
}
