<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.airsnap.cryptograph.ui.MainController">
   <center>
      <TabPane fx:id="mainTabPane" tabClosingPolicy="UNAVAILABLE">
         <tabs>
            <!-- Generate Cryptograph Tab -->
            <Tab text="Generate Cryptograph">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox spacing="10.0">
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                           
                           <!-- Demographic Information Section -->
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Demographic Information" />
                           <Separator />
                           
                           <GridPane hgap="10.0" vgap="10.0">
                              <columnConstraints>
                                 <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                 <ColumnConstraints hgrow="ALWAYS" />
                                 <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                 <ColumnConstraints hgrow="ALWAYS" />
                              </columnConstraints>
                              
                              <Label text="First Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="firstNameField" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              
                              <Label text="Last Name:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <TextField fx:id="lastNameField" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                              
                              <Label text="Date of Birth:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <DatePicker fx:id="dateOfBirthPicker" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              
                              <Label text="Gender:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <ComboBox fx:id="genderComboBox" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                              
                              <Label text="Nationality:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <TextField fx:id="nationalityField" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              
                              <Label text="Document Number:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                              <TextField fx:id="documentNumberField" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                              
                              <Label text="Document Type:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                              <ComboBox fx:id="documentTypeComboBox" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </GridPane>
                           
                           <!-- Biometric Data Section -->
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Biometric Data" />
                           <Separator />
                           
                           <HBox spacing="20.0">
                              <VBox alignment="CENTER" spacing="10.0">
                                 <Label text="Face Image" />
                                 <ImageView fx:id="faceImageView" fitHeight="150.0" fitWidth="150.0" pickOnBounds="true" preserveRatio="true" />
                                 <Button fx:id="selectFaceImageButton" text="Select Face Image" />
                              </VBox>
                              
                              <VBox alignment="CENTER" spacing="10.0">
                                 <Label text="Fingerprint Image" />
                                 <ImageView fx:id="fingerprintImageView" fitHeight="150.0" fitWidth="150.0" pickOnBounds="true" preserveRatio="true" />
                                 <Button fx:id="selectFingerprintImageButton" text="Select Fingerprint Image" />
                              </VBox>
                           </HBox>
                           
                           <!-- Generation Controls -->
                           <HBox alignment="CENTER" spacing="10.0">
                              <Button fx:id="generateCryptographButton" style="-fx-font-size: 14px;" text="Generate Cryptograph" />
                              <ProgressBar fx:id="generationProgressBar" visible="false" />
                           </HBox>
                           
                           <!-- Results -->
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Generation Result" />
                           <TextArea fx:id="generationResultArea" editable="false" prefRowCount="8" wrapText="true" />
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>
            
            <!-- Scan Cryptograph Tab -->
            <Tab text="Scan Cryptograph">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Cryptograph Scanning" />
                     <Separator />
                     
                     <!-- File Selection -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Button fx:id="selectCryptographFileButton" text="Select Cryptograph File" />
                        <Label fx:id="selectedCryptographLabel" text="No file selected" />
                     </HBox>
                     
                     <!-- Scan Controls -->
                     <HBox alignment="CENTER" spacing="10.0">
                        <Button fx:id="scanCryptographButton" style="-fx-font-size: 14px;" text="Scan Cryptograph" />
                        <ProgressBar fx:id="scanProgressBar" visible="false" />
                     </HBox>
                     
                     <!-- Results -->
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Scan Results" />
                     <TextArea fx:id="scanResultArea" editable="false" prefRowCount="12" wrapText="true" />
                     
                     <!-- Results Table -->
                     <TableView fx:id="scanResultTable" prefHeight="200.0">
                        <columns>
                           <TableColumn prefWidth="200.0" text="Cryptograph ID" />
                           <TableColumn prefWidth="100.0" text="Result" />
                           <TableColumn prefWidth="100.0" text="Confidence" />
                           <TableColumn prefWidth="150.0" text="Timestamp" />
                        </columns>
                     </TableView>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Compare Biometrics Tab -->
            <Tab text="Compare Biometrics">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Biometric Comparison" />
                     <Separator />
                     
                     <!-- File Selection -->
                     <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Button fx:id="selectFirstBiometricButton" text="Select First Biometric" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="firstBiometricLabel" text="No file selected" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Button fx:id="selectSecondBiometricButton" text="Select Second Biometric" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="secondBiometricLabel" text="No file selected" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Comparison Type:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <ComboBox fx:id="comparisonTypeComboBox" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                     </GridPane>
                     
                     <!-- Comparison Controls -->
                     <HBox alignment="CENTER" spacing="10.0">
                        <Button fx:id="performComparisonButton" style="-fx-font-size: 14px;" text="Perform Comparison" />
                        <ProgressBar fx:id="comparisonProgressBar" visible="false" />
                     </HBox>
                     
                     <!-- Results -->
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Comparison Results" />
                     <TextArea fx:id="comparisonResultArea" editable="false" prefRowCount="12" wrapText="true" />
                  </VBox>
               </content>
            </Tab>
            
            <!-- History Tab -->
            <Tab text="View History">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="System History and Audit" />
                     <Separator />
                     
                     <!-- History Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label text="History Type:" />
                        <ComboBox fx:id="historyTypeComboBox" />
                        
                        <Label text="Start Date:" />
                        <DatePicker fx:id="historyStartDatePicker" />
                        
                        <Label text="End Date:" />
                        <DatePicker fx:id="historyEndDatePicker" />
                        
                        <Button fx:id="refreshHistoryButton" text="Refresh" />
                     </HBox>
                     
                     <!-- History Table -->
                     <TableView fx:id="historyTable" prefHeight="200.0">
                        <columns>
                           <TableColumn prefWidth="100.0" text="Type" />
                           <TableColumn prefWidth="300.0" text="Description" />
                           <TableColumn prefWidth="150.0" text="Timestamp" />
                           <TableColumn prefWidth="100.0" text="Status" />
                        </columns>
                     </TableView>
                     
                     <!-- History Details -->
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Details" />
                     <TextArea fx:id="historyDetailsArea" editable="false" prefRowCount="10" wrapText="true" />
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </center>
</BorderPane>
