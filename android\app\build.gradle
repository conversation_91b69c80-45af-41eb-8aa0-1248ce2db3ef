plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.airsnap.biometric_app"
    compileSdk 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.airsnap.biometric_app"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.multidex:multidex:2.0.1'
    
    // AirSnap Libraries - Add all .aar files from libs directory
    implementation files('../../libs/T5CryptographClient-release.aar')
    implementation files('../../libs/CryptographReader-release.aar')
    implementation files('../../libs/OmnimatchUtil-release.aar')
    implementation files('../../libs/AirsnapFaceUI-release.aar')
    implementation files('../../libs/AirsnapFingerUI-release.aar')
    implementation files('../../libs/TLVDecode-release.aar')
    implementation files('../../libs/T5AirSnap-release.aar')
    implementation files('../../libs/airsnap-face-pro-core-1.2.3.aar')
    implementation files('../../libs/android-wrapper-4.5.1.aar')
    implementation files('../../libs/opencv_460-release.aar')
    implementation files('../../libs/t5ncnn-release.aar')
    implementation files('../../libs/t5opencv-release.aar')
    
    // Camera and Image Processing
    implementation 'androidx.camera:camera-core:1.3.0'
    implementation 'androidx.camera:camera-camera2:1.3.0'
    implementation 'androidx.camera:camera-lifecycle:1.3.0'
    implementation 'androidx.camera:camera-view:1.3.0'
    implementation 'androidx.camera:camera-extensions:1.3.0'

    // Image Processing and Computer Vision
    implementation 'androidx.exifinterface:exifinterface:1.3.6'
    implementation 'com.github.bumptech.glide:glide:4.15.1'

    // Biometric Processing
    implementation 'androidx.biometric:biometric:1.1.0'
    implementation 'androidx.biometric:biometric-ktx:1.2.0-alpha05'

    // Database
    implementation 'androidx.room:room-runtime:2.5.0'
    annotationProcessor 'androidx.room:room-compiler:2.5.0'

    // JSON Processing
    implementation 'com.google.code.gson:gson:2.10.1'

    // File Operations and Utilities
    implementation 'commons-io:commons-io:2.11.0'
    implementation 'androidx.work:work-runtime:2.8.1'
    implementation 'androidx.concurrent:concurrent-futures:1.1.0'

    // ML Kit for enhanced biometric processing
    implementation 'com.google.mlkit:face-detection:16.1.5'
    implementation 'com.google.android.gms:play-services-mlkit-face-detection:17.1.0'
}
