/* AirSnap Computer Vision Test Application Styles */

.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Segoe UI", Arial, sans-serif;
}

/* Menu Bar Styling */
.menu-bar {
    -fx-background-color: #2c3e50;
}

.menu-bar .menu {
    -fx-text-fill: white;
}

.menu-bar .menu:hover {
    -fx-background-color: #34495e;
}

.menu-item {
    -fx-background-color: white;
}

.menu-item:hover {
    -fx-background-color: #ecf0f1;
}

/* Button Styling */
.button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #2980b9;
}

.button:pressed {
    -fx-background-color: #21618c;
}

/* Image View Styling */
.image-view {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 10, 0, 0, 0);
}

/* Text Area Styling */
.text-area {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-font-family: "Consolas", "Monaco", monospace;
    -fx-font-size: 12px;
}

.text-area .content {
    -fx-background-color: white;
    -fx-background-radius: 5px;
}

/* Label Styling */
.label {
    -fx-text-fill: #2c3e50;
}

/* Status Bar Styling */
.status-label {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #2c3e50;
    -fx-padding: 5px 10px;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px 0 0 0;
}

/* Control Panel Styling */
.control-panel {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

/* Dialog Styling */
.dialog-pane {
    -fx-background-color: white;
}

.dialog-pane .header-panel {
    -fx-background-color: #3498db;
}

.dialog-pane .header-panel .label {
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

/* Choice Dialog Styling */
.choice-dialog .combo-box {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-radius: 3px;
    -fx-background-radius: 3px;
}

/* Scroll Pane Styling */
.scroll-pane {
    -fx-background-color: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .corner {
    -fx-background-color: #ecf0f1;
}

/* Progress Indicator */
.progress-indicator {
    -fx-progress-color: #3498db;
}

/* Separator Styling */
.separator .line {
    -fx-border-color: #bdc3c7;
}

/* Tooltip Styling */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-background-radius: 3px;
    -fx-font-size: 11px;
}
