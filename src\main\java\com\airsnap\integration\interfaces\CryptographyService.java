package com.airsnap.integration.interfaces;

import java.security.PublicKey;
import java.security.PrivateKey;
import java.util.Map;

/**
 * Interface for Cryptography functionality
 * Represents CryptographReader and T5CryptographClient libraries
 */
public interface CryptographyService {
    
    /**
     * Initialize the cryptography service
     * @return true if initialization successful
     */
    boolean initialize();
    
    /**
     * Generate key pair for cryptographic operations
     * @param keySize Key size in bits
     * @return Generated key pair
     */
    CryptoKeyPair generateKeyPair(int keySize);
    
    /**
     * Encrypt data using public key
     * @param data Data to encrypt
     * @param publicKey Public key for encryption
     * @return Encrypted data
     */
    byte[] encrypt(byte[] data, PublicKey publicKey);
    
    /**
     * Decrypt data using private key
     * @param encryptedData Encrypted data
     * @param privateKey Private key for decryption
     * @return Decrypted data
     */
    byte[] decrypt(byte[] encryptedData, PrivateKey privateKey);
    
    /**
     * Sign data using private key
     * @param data Data to sign
     * @param privateKey Private key for signing
     * @return Digital signature
     */
    byte[] signData(byte[] data, PrivateKey privateKey);
    
    /**
     * Verify signature using public key
     * @param data Original data
     * @param signature Digital signature
     * @param publicKey Public key for verification
     * @return true if signature is valid
     */
    boolean verifySignature(byte[] data, byte[] signature, PublicKey publicKey);
    
    /**
     * Generate hash of data
     * @param data Input data
     * @param algorithm Hash algorithm (SHA-256, SHA-512, etc.)
     * @return Hash value
     */
    byte[] generateHash(byte[] data, String algorithm);
    
    /**
     * Read cryptographic data from secure element
     * @param elementId Secure element identifier
     * @return Cryptographic data
     */
    CryptographicData readSecureElement(String elementId);
    
    /**
     * Write cryptographic data to secure element
     * @param elementId Secure element identifier
     * @param data Cryptographic data to write
     * @return true if write successful
     */
    boolean writeSecureElement(String elementId, CryptographicData data);
    
    /**
     * Validate certificate chain
     * @param certificateChain Certificate chain to validate
     * @return Validation result
     */
    CertificateValidationResult validateCertificateChain(byte[][] certificateChain);
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Inner classes for results
    public static class CryptoKeyPair {
        private final PublicKey publicKey;
        private final PrivateKey privateKey;
        
        public CryptoKeyPair(PublicKey publicKey, PrivateKey privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }
        
        // Getters
        public PublicKey getPublicKey() { return publicKey; }
        public PrivateKey getPrivateKey() { return privateKey; }
    }
    
    public static class CryptographicData {
        private final byte[] data;
        private final String algorithm;
        private final Map<String, Object> metadata;
        
        public CryptographicData(byte[] data, String algorithm, Map<String, Object> metadata) {
            this.data = data;
            this.algorithm = algorithm;
            this.metadata = metadata;
        }
        
        // Getters
        public byte[] getData() { return data; }
        public String getAlgorithm() { return algorithm; }
        public Map<String, Object> getMetadata() { return metadata; }
    }
    
    public static class CertificateValidationResult {
        private final boolean valid;
        private final String message;
        private final Map<String, Object> details;
        
        public CertificateValidationResult(boolean valid, String message, Map<String, Object> details) {
            this.valid = valid;
            this.message = message;
            this.details = details;
        }
        
        // Getters
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public Map<String, Object> getDetails() { return details; }
    }
}
