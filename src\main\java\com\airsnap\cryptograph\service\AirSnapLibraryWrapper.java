package com.airsnap.cryptograph.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JNI wrapper for AirSnap native libraries
 * This class provides Java bindings to the native AirSnap cryptograph libraries
 */
public class AirSnapLibraryWrapper {
    private static final Logger logger = LoggerFactory.getLogger(AirSnapLibraryWrapper.class);
    
    // Load native libraries
    static {
        try {
            // Load the native libraries in the correct order
            System.loadLibrary("t5opencv");
            System.loadLibrary("t5ncnn");
            System.loadLibrary("opencv_java4");
            System.loadLibrary("airsnap-face-pro-core");
            System.loadLibrary("T5AirSnap");
            System.loadLibrary("T5CryptographClient");
            System.loadLibrary("CryptographReader");
            System.loadLibrary("OmnimatchUtil");
            System.loadLibrary("TLVDecode");
            logger.info("AirSnap native libraries loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            logger.error("Failed to load AirSnap native libraries", e);
            throw new RuntimeException("Native library loading failed", e);
        }
    }
    
    // Native method declarations for T5CryptographClient
    
    /**
     * Initialize the T5 Cryptograph Client
     * @param licenseKey License key for the T5 system
     * @return true if initialization successful
     */
    public native boolean initializeT5Client(String licenseKey);
    
    /**
     * Generate cryptograph from demographic and biometric data
     * @param demographicData JSON string containing demographic information
     * @param faceImageData Face image data as byte array
     * @param fingerprintImageData Fingerprint image data as byte array
     * @return Generated cryptograph data as byte array
     */
    public native byte[] generateCryptograph(String demographicData, byte[] faceImageData, byte[] fingerprintImageData);
    
    /**
     * Extract face template from image
     * @param faceImageData Face image data as byte array
     * @return Face template as byte array
     */
    public native byte[] extractFaceTemplate(byte[] faceImageData);
    
    /**
     * Extract fingerprint template from image
     * @param fingerprintImageData Fingerprint image data as byte array
     * @return Fingerprint template as byte array
     */
    public native byte[] extractFingerprintTemplate(byte[] fingerprintImageData);
    
    /**
     * Extract fingerprint minutiae from image
     * @param fingerprintImageData Fingerprint image data as byte array
     * @return Fingerprint minutiae as byte array
     */
    public native byte[] extractFingerprintMinutiae(byte[] fingerprintImageData);
    
    // Native method declarations for CryptographReader
    
    /**
     * Initialize the Cryptograph Reader
     * @return true if initialization successful
     */
    public native boolean initializeCryptographReader();
    
    /**
     * Scan and decode cryptograph data
     * @param cryptographData Cryptograph data as byte array
     * @return Decoded information as JSON string
     */
    public native String scanCryptograph(byte[] cryptographData);
    
    /**
     * Extract demographic information from cryptograph
     * @param cryptographData Cryptograph data as byte array
     * @return Demographic information as JSON string
     */
    public native String extractDemographicInfo(byte[] cryptographData);
    
    /**
     * Extract biometric templates from cryptograph
     * @param cryptographData Cryptograph data as byte array
     * @return Biometric templates as byte array
     */
    public native byte[] extractBiometricTemplates(byte[] cryptographData);
    
    // Native method declarations for OmnimatchUtil
    
    /**
     * Initialize OmniMatch utility
     * @return true if initialization successful
     */
    public native boolean initializeOmniMatch();
    
    /**
     * Compare two fingerprint templates
     * @param template1 First fingerprint template
     * @param template2 Second fingerprint template
     * @return Comparison score (0.0 to 1.0)
     */
    public native double compareFingerprintTemplates(byte[] template1, byte[] template2);
    
    /**
     * Compare two face templates
     * @param template1 First face template
     * @param template2 Second face template
     * @return Comparison score (0.0 to 1.0)
     */
    public native double compareFaceTemplates(byte[] template1, byte[] template2);
    
    /**
     * Compare two cryptographs
     * @param cryptograph1 First cryptograph data
     * @param cryptograph2 Second cryptograph data
     * @return Comparison result as JSON string
     */
    public native String compareCryptographs(byte[] cryptograph1, byte[] cryptograph2);
    
    // Native method declarations for TLVDecode
    
    /**
     * Decode TLV (Tag-Length-Value) data
     * @param tlvData TLV encoded data
     * @return Decoded data as JSON string
     */
    public native String decodeTLV(byte[] tlvData);
    
    /**
     * Encode data to TLV format
     * @param jsonData Data to encode as JSON string
     * @return TLV encoded data as byte array
     */
    public native byte[] encodeTLV(String jsonData);
    
    // Utility methods
    
    /**
     * Get library version information
     * @return Version information as JSON string
     */
    public native String getLibraryVersions();
    
    /**
     * Check if all libraries are properly initialized
     * @return true if all libraries are ready
     */
    public native boolean isSystemReady();
    
    /**
     * Cleanup and release native resources
     */
    public native void cleanup();
    
    // Java wrapper methods with error handling
    
    /**
     * Safe wrapper for cryptograph generation with error handling
     */
    public byte[] safeGenerateCryptograph(String demographicData, byte[] faceImageData, byte[] fingerprintImageData) {
        try {
            return generateCryptograph(demographicData, faceImageData, fingerprintImageData);
        } catch (Exception e) {
            logger.error("Error generating cryptograph", e);
            return null;
        }
    }
    
    /**
     * Safe wrapper for cryptograph scanning with error handling
     */
    public String safeScanCryptograph(byte[] cryptographData) {
        try {
            return scanCryptograph(cryptographData);
        } catch (Exception e) {
            logger.error("Error scanning cryptograph", e);
            return null;
        }
    }
    
    /**
     * Safe wrapper for fingerprint comparison with error handling
     */
    public double safeCompareFingerprintTemplates(byte[] template1, byte[] template2) {
        try {
            return compareFingerprintTemplates(template1, template2);
        } catch (Exception e) {
            logger.error("Error comparing fingerprint templates", e);
            return -1.0;
        }
    }
}
