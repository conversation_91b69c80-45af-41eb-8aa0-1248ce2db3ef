import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import '../services/camera_service.dart';

class CameraCaptureWidget extends StatefulWidget {
  final String title;
  final String subtitle;
  final Function(File) onImageCaptured;
  final CameraLensDirection preferredLens;
  final bool showGalleryOption;
  final Widget? overlayWidget;

  const CameraCaptureWidget({
    super.key,
    required this.title,
    required this.onImageCaptured,
    this.subtitle = 'Position the subject in the frame and tap capture',
    this.preferredLens = CameraLensDirection.front,
    this.showGalleryOption = true,
    this.overlayWidget,
  });

  @override
  State<CameraCaptureWidget> createState() => _CameraCaptureWidgetState();
}

class _CameraCaptureWidgetState extends State<CameraCaptureWidget>
    with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isInitializing = true;
  bool _isCapturing = false;
  String? _error;
  File? _capturedImage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _controller?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  Future<void> _initializeCamera() async {
    setState(() {
      _isInitializing = true;
      _error = null;
    });

    try {
      final bool hasPermission = await CameraService.requestCameraPermission();
      if (!hasPermission) {
        setState(() {
          _error = 'Camera permission denied';
          _isInitializing = false;
        });
        return;
      }

      final CameraDescription? camera = widget.preferredLens == CameraLensDirection.front
          ? CameraService.frontCamera
          : CameraService.backCamera;

      _controller = await CameraService.initializeController(
        camera: camera,
        resolution: ResolutionPreset.high,
      );

      if (_controller != null) {
        setState(() {
          _isInitializing = false;
        });
      } else {
        setState(() {
          _error = 'Failed to initialize camera';
          _isInitializing = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Camera error: $e';
        _isInitializing = false;
      });
    }
  }

  Future<void> _capturePhoto() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    setState(() {
      _isCapturing = true;
    });

    try {
      final String customPath = await CameraService.createCustomPath(
        prefix: widget.title.toLowerCase().replaceAll(' ', '_'),
      );

      final File? imageFile = await CameraService.capturePhoto(
        customPath: customPath,
      );

      if (imageFile != null) {
        setState(() {
          _capturedImage = imageFile;
        });
        widget.onImageCaptured(imageFile);
      } else {
        _showError('Failed to capture photo');
      }
    } catch (e) {
      _showError('Capture error: $e');
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final File? imageFile = await CameraService.pickFromGallery();
      if (imageFile != null) {
        setState(() {
          _capturedImage = imageFile;
        });
        widget.onImageCaptured(imageFile);
      }
    } catch (e) {
      _showError('Gallery error: $e');
    }
  }

  void _showError(String message) {
    setState(() {
      _error = message;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _retakePhoto() {
    setState(() {
      _capturedImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          if (widget.showGalleryOption)
            IconButton(
              onPressed: _pickFromGallery,
              icon: const Icon(Icons.photo_library),
              tooltip: 'Choose from Gallery',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_capturedImage != null) {
      return _buildPreviewScreen();
    }

    if (_isInitializing) {
      return _buildLoadingScreen();
    }

    if (_error != null) {
      return _buildErrorScreen();
    }

    if (_controller == null || !_controller!.value.isInitialized) {
      return _buildErrorScreen();
    }

    return _buildCameraScreen();
  }

  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'Initializing camera...',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? 'Camera not available',
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeCamera,
            child: const Text('Retry'),
          ),
          if (widget.showGalleryOption) ...[
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: _pickFromGallery,
              icon: const Icon(Icons.photo_library),
              label: const Text('Choose from Gallery'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCameraScreen() {
    return Stack(
      children: [
        // Camera preview
        Positioned.fill(
          child: CameraPreview(_controller!),
        ),

        // Overlay widget (for face/fingerprint guides)
        if (widget.overlayWidget != null)
          Positioned.fill(
            child: widget.overlayWidget!,
          ),

        // Top instruction bar
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: Text(
              widget.subtitle,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        // Bottom controls
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Gallery button
                if (widget.showGalleryOption)
                  IconButton(
                    onPressed: _pickFromGallery,
                    icon: const Icon(
                      Icons.photo_library,
                      color: Colors.white,
                      size: 32,
                    ),
                  )
                else
                  const SizedBox(width: 48),

                // Capture button
                GestureDetector(
                  onTap: _isCapturing ? null : _capturePhoto,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 4,
                      ),
                    ),
                    child: _isCapturing
                        ? const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Container(
                            margin: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                  ),
                ),

                // Switch camera button
                IconButton(
                  onPressed: _switchCamera,
                  icon: const Icon(
                    Icons.flip_camera_ios,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewScreen() {
    return Column(
      children: [
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: const BoxDecoration(color: Colors.black),
            child: Image.file(
              _capturedImage!,
              fit: BoxFit.contain,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.black,
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _retakePhoto,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retake'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[700],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.check),
                  label: const Text('Use Photo'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _switchCamera() async {
    if (_controller == null) return;

    final CameraDescription? newCamera = 
        _controller!.description.lensDirection == CameraLensDirection.front
            ? CameraService.backCamera
            : CameraService.frontCamera;

    if (newCamera != null) {
      await _controller!.dispose();
      _controller = await CameraService.initializeController(
        camera: newCamera,
        resolution: ResolutionPreset.high,
      );
      setState(() {});
    }
  }
}
