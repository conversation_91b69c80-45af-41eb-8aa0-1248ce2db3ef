package com.airsnap.integration.interfaces;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * Interface for Computer Vision functionality
 * Represents opencv_460, t5ncnn, and t5opencv libraries
 */
public interface ComputerVisionService {
    
    /**
     * Initialize the computer vision service
     * @return true if initialization successful
     */
    boolean initialize();
    
    /**
     * Load neural network model
     * @param modelPath Path to the model file
     * @param modelType Type of neural network model
     * @return true if model loaded successfully
     */
    boolean loadNeuralNetworkModel(String modelPath, ModelType modelType);
    
    /**
     * Process image with neural network
     * @param image Input image
     * @param modelName Name of the loaded model
     * @return Neural network inference result
     */
    NeuralNetworkResult processWithNeuralNetwork(BufferedImage image, String modelName);
    
    /**
     * Detect objects in image
     * @param image Input image
     * @return List of detected objects
     */
    List<ObjectDetectionResult> detectObjects(BufferedImage image);
    
    /**
     * Apply image filters
     * @param image Input image
     * @param filters List of filters to apply
     * @return Filtered image
     */
    BufferedImage applyImageFilters(BufferedImage image, List<ImageFilter> filters);
    
    /**
     * Extract image features
     * @param image Input image
     * @param featureType Type of features to extract
     * @return Feature vector
     */
    float[] extractImageFeatures(BufferedImage image, FeatureType featureType);
    
    /**
     * Perform image segmentation
     * @param image Input image
     * @param segmentationType Type of segmentation
     * @return Segmented image
     */
    BufferedImage performImageSegmentation(BufferedImage image, SegmentationType segmentationType);
    
    /**
     * Calculate image similarity
     * @param image1 First image
     * @param image2 Second image
     * @param similarityMetric Similarity calculation method
     * @return Similarity score (0.0 to 1.0)
     */
    double calculateImageSimilarity(BufferedImage image1, BufferedImage image2, SimilarityMetric similarityMetric);
    
    /**
     * Enhance image quality
     * @param image Input image
     * @param enhancementType Type of enhancement
     * @return Enhanced image
     */
    BufferedImage enhanceImageQuality(BufferedImage image, EnhancementType enhancementType);
    
    /**
     * Perform optical character recognition
     * @param image Input image
     * @return Recognized text
     */
    String performOCR(BufferedImage image);
    
    /**
     * Get processing statistics
     * @return Processing statistics
     */
    ProcessingStatistics getProcessingStatistics();
    
    /**
     * Cleanup resources
     */
    void cleanup();
    
    // Enums and inner classes
    public enum ModelType {
        CLASSIFICATION,
        DETECTION,
        SEGMENTATION,
        FACE_RECOGNITION,
        FEATURE_EXTRACTION
    }
    
    public enum FeatureType {
        SIFT,
        SURF,
        ORB,
        HARRIS_CORNERS,
        FAST,
        BRIEF
    }
    
    public enum SegmentationType {
        SEMANTIC,
        INSTANCE,
        PANOPTIC,
        WATERSHED,
        REGION_GROWING
    }
    
    public enum SimilarityMetric {
        COSINE,
        EUCLIDEAN,
        MANHATTAN,
        CORRELATION,
        HISTOGRAM
    }
    
    public enum EnhancementType {
        NOISE_REDUCTION,
        SHARPENING,
        CONTRAST_ENHANCEMENT,
        BRIGHTNESS_ADJUSTMENT,
        HISTOGRAM_EQUALIZATION
    }
    
    public static class ImageFilter {
        private final String name;
        private final Map<String, Object> parameters;
        
        public ImageFilter(String name, Map<String, Object> parameters) {
            this.name = name;
            this.parameters = parameters;
        }
        
        // Getters
        public String getName() { return name; }
        public Map<String, Object> getParameters() { return parameters; }
    }
    
    public static class NeuralNetworkResult {
        private final float[] output;
        private final Map<String, Float> predictions;
        private final long processingTime;
        
        public NeuralNetworkResult(float[] output, Map<String, Float> predictions, long processingTime) {
            this.output = output;
            this.predictions = predictions;
            this.processingTime = processingTime;
        }
        
        // Getters
        public float[] getOutput() { return output; }
        public Map<String, Float> getPredictions() { return predictions; }
        public long getProcessingTime() { return processingTime; }
    }
    
    public static class ObjectDetectionResult {
        private final String className;
        private final float confidence;
        private final int x, y, width, height;
        
        public ObjectDetectionResult(String className, float confidence, int x, int y, int width, int height) {
            this.className = className;
            this.confidence = confidence;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        // Getters
        public String getClassName() { return className; }
        public float getConfidence() { return confidence; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
    }
    
    public static class ProcessingStatistics {
        private final long totalProcessedImages;
        private final double averageProcessingTime;
        private final Map<String, Long> operationCounts;
        
        public ProcessingStatistics(long totalProcessedImages, double averageProcessingTime, Map<String, Long> operationCounts) {
            this.totalProcessedImages = totalProcessedImages;
            this.averageProcessingTime = averageProcessingTime;
            this.operationCounts = operationCounts;
        }
        
        // Getters
        public long getTotalProcessedImages() { return totalProcessedImages; }
        public double getAverageProcessingTime() { return averageProcessingTime; }
        public Map<String, Long> getOperationCounts() { return operationCounts; }
    }
}
