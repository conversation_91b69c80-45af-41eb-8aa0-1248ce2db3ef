package com.airsnap.cryptograph.service;

import com.airsnap.cryptograph.database.DatabaseManager;
import com.airsnap.cryptograph.model.Cryptograph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for tracking and retrieving history of cryptograph operations and audit logs
 */
public class HistoryAndAuditService {
    private static final Logger logger = LoggerFactory.getLogger(HistoryAndAuditService.class);
    
    private final DatabaseManager dbManager;
    
    public HistoryAndAuditService(DatabaseManager dbManager) {
        this.dbManager = dbManager;
    }
    
    /**
     * Get generation history for all cryptographs
     */
    public List<GenerationHistoryRecord> getGenerationHistory() throws SQLException {
        return getGenerationHistory(null, null);
    }
    
    /**
     * Get generation history within a date range
     */
    public List<GenerationHistoryRecord> getGenerationHistory(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        StringBuilder sql = new StringBuilder("""
            SELECT c.id, c.cryptograph_id, c.generation_method, c.created_at,
                   d.first_name, d.last_name, d.document_number, d.document_type,
                   LENGTH(c.cryptograph_data) as data_size
            FROM cryptographs c
            JOIN demographic_info d ON c.demographic_id = d.id
        """);
        
        List<Object> params = new ArrayList<>();
        
        if (startDate != null && endDate != null) {
            sql.append(" WHERE c.created_at BETWEEN ? AND ?");
            params.add(startDate);
            params.add(endDate);
        } else if (startDate != null) {
            sql.append(" WHERE c.created_at >= ?");
            params.add(startDate);
        } else if (endDate != null) {
            sql.append(" WHERE c.created_at <= ?");
            params.add(endDate);
        }
        
        sql.append(" ORDER BY c.created_at DESC");
        
        List<GenerationHistoryRecord> history = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql.toString())) {
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    history.add(new GenerationHistoryRecord(
                        rs.getLong("id"),
                        rs.getString("cryptograph_id"),
                        rs.getString("first_name") + " " + rs.getString("last_name"),
                        rs.getString("document_number"),
                        rs.getString("document_type"),
                        rs.getString("generation_method"),
                        rs.getInt("data_size"),
                        rs.getTimestamp("created_at").toLocalDateTime()
                    ));
                }
            }
        }
        
        logger.info("Retrieved {} generation history records", history.size());
        return history;
    }
    
    /**
     * Get scan history for all cryptographs
     */
    public List<ScanHistoryRecord> getScanHistory() throws SQLException {
        return getScanHistory(null, null);
    }
    
    /**
     * Get scan history within a date range
     */
    public List<ScanHistoryRecord> getScanHistory(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        StringBuilder sql = new StringBuilder("""
            SELECT id, cryptograph_id, scan_result, scan_confidence, scan_method, scanned_at,
                   LENGTH(extracted_data) as extracted_data_size
            FROM scan_history
        """);
        
        List<Object> params = new ArrayList<>();
        
        if (startDate != null && endDate != null) {
            sql.append(" WHERE scanned_at BETWEEN ? AND ?");
            params.add(startDate);
            params.add(endDate);
        } else if (startDate != null) {
            sql.append(" WHERE scanned_at >= ?");
            params.add(startDate);
        } else if (endDate != null) {
            sql.append(" WHERE scanned_at <= ?");
            params.add(endDate);
        }
        
        sql.append(" ORDER BY scanned_at DESC");
        
        List<ScanHistoryRecord> history = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql.toString())) {
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    history.add(new ScanHistoryRecord(
                        rs.getLong("id"),
                        rs.getString("cryptograph_id"),
                        rs.getString("scan_result"),
                        rs.getDouble("scan_confidence"),
                        rs.getString("scan_method"),
                        rs.getInt("extracted_data_size"),
                        rs.getTimestamp("scanned_at").toLocalDateTime()
                    ));
                }
            }
        }
        
        logger.info("Retrieved {} scan history records", history.size());
        return history;
    }
    
    /**
     * Get comparison history for all operations
     */
    public List<ComparisonHistoryRecord> getComparisonHistory() throws SQLException {
        return getComparisonHistory(null, null);
    }
    
    /**
     * Get comparison history within a date range
     */
    public List<ComparisonHistoryRecord> getComparisonHistory(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        StringBuilder sql = new StringBuilder("""
            SELECT id, comparison_type, source_id, target_id, comparison_score, 
                   match_result, comparison_method, compared_at,
                   LENGTH(comparison_details) as details_size
            FROM comparison_history
        """);
        
        List<Object> params = new ArrayList<>();
        
        if (startDate != null && endDate != null) {
            sql.append(" WHERE compared_at BETWEEN ? AND ?");
            params.add(startDate);
            params.add(endDate);
        } else if (startDate != null) {
            sql.append(" WHERE compared_at >= ?");
            params.add(startDate);
        } else if (endDate != null) {
            sql.append(" WHERE compared_at <= ?");
            params.add(endDate);
        }
        
        sql.append(" ORDER BY compared_at DESC");
        
        List<ComparisonHistoryRecord> history = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql.toString())) {
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    history.add(new ComparisonHistoryRecord(
                        rs.getLong("id"),
                        rs.getString("comparison_type"),
                        rs.getString("source_id"),
                        rs.getString("target_id"),
                        rs.getDouble("comparison_score"),
                        rs.getBoolean("match_result"),
                        rs.getString("comparison_method"),
                        rs.getInt("details_size"),
                        rs.getTimestamp("compared_at").toLocalDateTime()
                    ));
                }
            }
        }
        
        logger.info("Retrieved {} comparison history records", history.size());
        return history;
    }
    
    /**
     * Get audit log entries
     */
    public List<AuditLogRecord> getAuditLog() throws SQLException {
        return getAuditLog(null, null, null);
    }
    
    /**
     * Get audit log entries with filters
     */
    public List<AuditLogRecord> getAuditLog(String operationType, String entityType, LocalDateTime startDate) throws SQLException {
        StringBuilder sql = new StringBuilder("""
            SELECT id, operation_type, entity_type, entity_id, user_info, 
                   operation_details, success, error_message, created_at
            FROM audit_log
        """);
        
        List<Object> params = new ArrayList<>();
        List<String> conditions = new ArrayList<>();
        
        if (operationType != null && !operationType.isEmpty()) {
            conditions.add("operation_type = ?");
            params.add(operationType);
        }
        
        if (entityType != null && !entityType.isEmpty()) {
            conditions.add("entity_type = ?");
            params.add(entityType);
        }
        
        if (startDate != null) {
            conditions.add("created_at >= ?");
            params.add(startDate);
        }
        
        if (!conditions.isEmpty()) {
            sql.append(" WHERE ").append(String.join(" AND ", conditions));
        }
        
        sql.append(" ORDER BY created_at DESC LIMIT 1000"); // Limit to prevent excessive results
        
        List<AuditLogRecord> auditLog = new ArrayList<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql.toString())) {
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    auditLog.add(new AuditLogRecord(
                        rs.getLong("id"),
                        rs.getString("operation_type"),
                        rs.getString("entity_type"),
                        rs.getString("entity_id"),
                        rs.getString("user_info"),
                        rs.getString("operation_details"),
                        rs.getBoolean("success"),
                        rs.getString("error_message"),
                        rs.getTimestamp("created_at").toLocalDateTime()
                    ));
                }
            }
        }
        
        logger.info("Retrieved {} audit log records", auditLog.size());
        return auditLog;
    }
    
    /**
     * Get system statistics
     */
    public SystemStatistics getSystemStatistics() throws SQLException {
        Map<String, Object> stats = new HashMap<>();
        
        // Cryptograph statistics
        String cryptographStatsSQL = """
            SELECT COUNT(*) as total_cryptographs,
                   AVG(LENGTH(cryptograph_data)) as avg_size,
                   MIN(created_at) as first_created,
                   MAX(created_at) as last_created
            FROM cryptographs
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(cryptographStatsSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("totalCryptographs", rs.getInt("total_cryptographs"));
                stats.put("avgCryptographSize", rs.getDouble("avg_size"));
                stats.put("firstCryptographCreated", rs.getTimestamp("first_created"));
                stats.put("lastCryptographCreated", rs.getTimestamp("last_created"));
            }
        }
        
        // Scan statistics
        String scanStatsSQL = """
            SELECT COUNT(*) as total_scans,
                   AVG(scan_confidence) as avg_confidence,
                   SUM(CASE WHEN scan_result = 'SUCCESS' THEN 1 ELSE 0 END) as successful_scans
            FROM scan_history
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(scanStatsSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("totalScans", rs.getInt("total_scans"));
                stats.put("avgScanConfidence", rs.getDouble("avg_confidence"));
                stats.put("successfulScans", rs.getInt("successful_scans"));
            }
        }
        
        // Comparison statistics
        String comparisonStatsSQL = """
            SELECT COUNT(*) as total_comparisons,
                   AVG(comparison_score) as avg_score,
                   SUM(CASE WHEN match_result = true THEN 1 ELSE 0 END) as successful_matches,
                   comparison_type,
                   COUNT(*) as type_count
            FROM comparison_history
            GROUP BY comparison_type
        """;
        
        Map<String, Integer> comparisonsByType = new HashMap<>();
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(comparisonStatsSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            int totalComparisons = 0;
            double totalScore = 0.0;
            int totalMatches = 0;
            
            while (rs.next()) {
                String type = rs.getString("comparison_type");
                int count = rs.getInt("type_count");
                comparisonsByType.put(type, count);
                
                totalComparisons += count;
                totalScore += rs.getDouble("avg_score") * count;
                totalMatches += rs.getInt("successful_matches");
            }
            
            stats.put("totalComparisons", totalComparisons);
            stats.put("avgComparisonScore", totalComparisons > 0 ? totalScore / totalComparisons : 0.0);
            stats.put("successfulMatches", totalMatches);
            stats.put("comparisonsByType", comparisonsByType);
        }
        
        // Demographic statistics
        String demographicStatsSQL = """
            SELECT COUNT(*) as total_demographics,
                   COUNT(DISTINCT nationality) as unique_nationalities,
                   COUNT(DISTINCT document_type) as unique_document_types
            FROM demographic_info
        """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(demographicStatsSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("totalDemographics", rs.getInt("total_demographics"));
                stats.put("uniqueNationalities", rs.getInt("unique_nationalities"));
                stats.put("uniqueDocumentTypes", rs.getInt("unique_document_types"));
            }
        }
        
        return new SystemStatistics(stats);
    }
    
    /**
     * Clean up old history records (older than specified days)
     */
    public int cleanupOldRecords(int daysToKeep) throws SQLException {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        int totalDeleted = 0;
        
        // Clean up scan history
        String deleteScanHistorySQL = "DELETE FROM scan_history WHERE scanned_at < ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(deleteScanHistorySQL)) {
            stmt.setObject(1, cutoffDate);
            int deleted = stmt.executeUpdate();
            totalDeleted += deleted;
            logger.info("Deleted {} old scan history records", deleted);
        }
        
        // Clean up comparison history
        String deleteComparisonHistorySQL = "DELETE FROM comparison_history WHERE compared_at < ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(deleteComparisonHistorySQL)) {
            stmt.setObject(1, cutoffDate);
            int deleted = stmt.executeUpdate();
            totalDeleted += deleted;
            logger.info("Deleted {} old comparison history records", deleted);
        }
        
        // Clean up audit log (keep more audit records - 90 days minimum)
        LocalDateTime auditCutoffDate = LocalDateTime.now().minusDays(Math.max(daysToKeep, 90));
        String deleteAuditLogSQL = "DELETE FROM audit_log WHERE created_at < ?";
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(deleteAuditLogSQL)) {
            stmt.setObject(1, auditCutoffDate);
            int deleted = stmt.executeUpdate();
            totalDeleted += deleted;
            logger.info("Deleted {} old audit log records", deleted);
        }
        
        // Log cleanup operation
        dbManager.logAudit("CLEANUP", "HISTORY", null, "SYSTEM", 
                         "Cleaned up " + totalDeleted + " old records", true, null);
        
        logger.info("Total cleanup: deleted {} old records", totalDeleted);
        return totalDeleted;
    }
    
    // Inner classes for history records
    
    public static class GenerationHistoryRecord {
        private final Long id;
        private final String cryptographId;
        private final String personName;
        private final String documentNumber;
        private final String documentType;
        private final String generationMethod;
        private final int dataSize;
        private final LocalDateTime createdAt;
        
        public GenerationHistoryRecord(Long id, String cryptographId, String personName, String documentNumber,
                                     String documentType, String generationMethod, int dataSize, LocalDateTime createdAt) {
            this.id = id;
            this.cryptographId = cryptographId;
            this.personName = personName;
            this.documentNumber = documentNumber;
            this.documentType = documentType;
            this.generationMethod = generationMethod;
            this.dataSize = dataSize;
            this.createdAt = createdAt;
        }
        
        // Getters
        public Long getId() { return id; }
        public String getCryptographId() { return cryptographId; }
        public String getPersonName() { return personName; }
        public String getDocumentNumber() { return documentNumber; }
        public String getDocumentType() { return documentType; }
        public String getGenerationMethod() { return generationMethod; }
        public int getDataSize() { return dataSize; }
        public LocalDateTime getCreatedAt() { return createdAt; }
    }
    
    public static class ScanHistoryRecord {
        private final Long id;
        private final String cryptographId;
        private final String scanResult;
        private final double scanConfidence;
        private final String scanMethod;
        private final int extractedDataSize;
        private final LocalDateTime scannedAt;
        
        public ScanHistoryRecord(Long id, String cryptographId, String scanResult, double scanConfidence,
                               String scanMethod, int extractedDataSize, LocalDateTime scannedAt) {
            this.id = id;
            this.cryptographId = cryptographId;
            this.scanResult = scanResult;
            this.scanConfidence = scanConfidence;
            this.scanMethod = scanMethod;
            this.extractedDataSize = extractedDataSize;
            this.scannedAt = scannedAt;
        }
        
        // Getters
        public Long getId() { return id; }
        public String getCryptographId() { return cryptographId; }
        public String getScanResult() { return scanResult; }
        public double getScanConfidence() { return scanConfidence; }
        public String getScanMethod() { return scanMethod; }
        public int getExtractedDataSize() { return extractedDataSize; }
        public LocalDateTime getScannedAt() { return scannedAt; }
    }
    
    public static class ComparisonHistoryRecord {
        private final Long id;
        private final String comparisonType;
        private final String sourceId;
        private final String targetId;
        private final double comparisonScore;
        private final boolean matchResult;
        private final String comparisonMethod;
        private final int detailsSize;
        private final LocalDateTime comparedAt;
        
        public ComparisonHistoryRecord(Long id, String comparisonType, String sourceId, String targetId,
                                     double comparisonScore, boolean matchResult, String comparisonMethod,
                                     int detailsSize, LocalDateTime comparedAt) {
            this.id = id;
            this.comparisonType = comparisonType;
            this.sourceId = sourceId;
            this.targetId = targetId;
            this.comparisonScore = comparisonScore;
            this.matchResult = matchResult;
            this.comparisonMethod = comparisonMethod;
            this.detailsSize = detailsSize;
            this.comparedAt = comparedAt;
        }
        
        // Getters
        public Long getId() { return id; }
        public String getComparisonType() { return comparisonType; }
        public String getSourceId() { return sourceId; }
        public String getTargetId() { return targetId; }
        public double getComparisonScore() { return comparisonScore; }
        public boolean isMatchResult() { return matchResult; }
        public String getComparisonMethod() { return comparisonMethod; }
        public int getDetailsSize() { return detailsSize; }
        public LocalDateTime getComparedAt() { return comparedAt; }
    }
    
    public static class AuditLogRecord {
        private final Long id;
        private final String operationType;
        private final String entityType;
        private final String entityId;
        private final String userInfo;
        private final String operationDetails;
        private final boolean success;
        private final String errorMessage;
        private final LocalDateTime createdAt;
        
        public AuditLogRecord(Long id, String operationType, String entityType, String entityId,
                            String userInfo, String operationDetails, boolean success, String errorMessage,
                            LocalDateTime createdAt) {
            this.id = id;
            this.operationType = operationType;
            this.entityType = entityType;
            this.entityId = entityId;
            this.userInfo = userInfo;
            this.operationDetails = operationDetails;
            this.success = success;
            this.errorMessage = errorMessage;
            this.createdAt = createdAt;
        }
        
        // Getters
        public Long getId() { return id; }
        public String getOperationType() { return operationType; }
        public String getEntityType() { return entityType; }
        public String getEntityId() { return entityId; }
        public String getUserInfo() { return userInfo; }
        public String getOperationDetails() { return operationDetails; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public LocalDateTime getCreatedAt() { return createdAt; }
    }
    
    public static class SystemStatistics {
        private final Map<String, Object> statistics;
        
        public SystemStatistics(Map<String, Object> statistics) {
            this.statistics = new HashMap<>(statistics);
        }
        
        public Map<String, Object> getStatistics() { return statistics; }
        
        public Object getStat(String key) { return statistics.get(key); }
        
        public int getIntStat(String key) {
            Object value = statistics.get(key);
            return value instanceof Number ? ((Number) value).intValue() : 0;
        }
        
        public double getDoubleStat(String key) {
            Object value = statistics.get(key);
            return value instanceof Number ? ((Number) value).doubleValue() : 0.0;
        }
    }
}
